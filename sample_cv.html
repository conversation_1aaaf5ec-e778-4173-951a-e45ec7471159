<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CV - <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .cv-container {
            display: flex;
            min-height: 100vh;
            max-width: 210mm;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a5f 100%);
            color: white;
            width: 35%;
            padding: 40px 30px;
            position: relative;
        }
        
        .main-content {
            flex: 1;
            padding: 40px 30px;
            background: white;
        }
        
        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid rgba(255,255,255,0.2);
            margin: 0 auto 30px;
            display: block;
            background: rgba(255,255,255,0.1);
        }
        
        .sidebar h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 300;
        }
        
        .sidebar .title {
            text-align: center;
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .sidebar-section {
            margin-bottom: 30px;
        }
        
        .sidebar-section h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            font-weight: 600;
        }
        
        .contact-item {
            margin-bottom: 12px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }
        
        .skill-item {
            margin-bottom: 15px;
        }
        
        .skill-name {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .skill-bar {
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .skill-progress {
            height: 100%;
            background: #4CAF50;
            border-radius: 3px;
        }
        
        .main-content h2 {
            color: #2c5aa0;
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2c5aa0;
            font-weight: 600;
        }
        
        .experience-item, .education-item, .project-item, .cert-item {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .experience-item:last-child, .education-item:last-child, 
        .project-item:last-child, .cert-item:last-child {
            border-bottom: none;
        }
        
        .item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .item-company {
            font-size: 1rem;
            color: #2c5aa0;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .item-date {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .item-description {
            font-size: 0.9rem;
            color: #555;
            line-height: 1.6;
        }
        
        @media print {
            body { margin: 0; }
            .cv-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <div class="sidebar">
            <div class="profile-photo"></div>
            <h1>John Doe</h1>
            <div class="title">Software Developer</div>
            
            <div class="sidebar-section">
                <h3>CONTACT</h3>
                <div class="contact-item">
                    <span>📧</span> <EMAIL>
                </div>
                <div class="contact-item">
                    <span>📱</span> +1 (555) 123-4567
                </div>
            </div>

            <div class="sidebar-section">
                <h3>SKILLS</h3>
                <div class="skill-item">
                    <div class="skill-name">JavaScript</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 90%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">PHP</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 85%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">React</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 80%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">MySQL</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <h2>PROFESSIONAL EXPERIENCE</h2>
            <div class="experience-item">
                <div class="item-title">Senior Software Developer</div>
                <div class="item-company">Tech Solutions Inc.</div>
                <div class="item-date">January 2022 - Present</div>
                <div class="item-description">
                    Led development of web applications using modern technologies. Collaborated with cross-functional teams to deliver high-quality software solutions. Mentored junior developers and implemented best practices.
                </div>
            </div>
            
            <div class="experience-item">
                <div class="item-title">Full Stack Developer</div>
                <div class="item-company">Digital Agency Co.</div>
                <div class="item-date">June 2020 - December 2021</div>
                <div class="item-description">
                    Developed and maintained client websites and web applications. Worked with PHP, JavaScript, and MySQL to create dynamic user experiences.
                </div>
            </div>

            <h2>EDUCATION</h2>
            <div class="education-item">
                <div class="item-title">Bachelor of Computer Science</div>
                <div class="item-company">University of Technology</div>
                <div class="item-date">2016-2020</div>
            </div>

            <h2>PROJECTS</h2>
            <div class="project-item">
                <div class="item-title">CV Builder Application <a href="#" style="color: #2c5aa0; text-decoration: none; font-size: 0.9rem;">[GitHub]</a></div>
                <div class="item-description">
                    A comprehensive web-based CV builder using PHP and MySQL. Features include user authentication, dynamic form handling, and professional CV generation.
                </div>
            </div>

            <h2>CERTIFICATIONS</h2>
            <div class="cert-item">
                <div class="item-title">AWS Certified Developer</div>
                <div class="item-company">Amazon Web Services</div>
                <div class="item-date">Issued: March 2023 | Expires: March 2026</div>
            </div>
        </div>
    </div>
</body>
</html>
