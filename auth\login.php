<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        header('Location: ../admin/dashboard.php');
    } else {
        header('Location: ../dashboard.php');
    }
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];

    if (empty($email) || empty($password)) {
        $error = 'Please enter both email and password.';
    } else {
        // Check user credentials (users only, not admins)
        $stmt = $pdo->prepare("SELECT id, full_name, email, password, user_type FROM users WHERE email = ? AND user_type = 'user'");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && verifyPassword($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['full_name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_type'] = $user['user_type'];

            // Redirect to user dashboard
            header('Location: ../dashboard.php');
            exit();
        } else {
            $error = 'Invalid user credentials.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Login - CV Builder</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .user-login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            position: relative;
            overflow: hidden;
        }

        .user-login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite;
        }

        .user-login-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 1.5rem;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: var(--shadow-xl);
            position: relative;
            z-index: 1;
            backdrop-filter: blur(20px);
            animation: cardSlideIn 0.8s ease-out;
        }

        .user-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .user-logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        .user-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .user-description {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .user-form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .user-form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .user-form-control {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            font-size: 1rem;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .user-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            transform: translateY(-2px);
            background: var(--bg-tertiary);
        }

        .user-btn {
            width: 100%;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--gradient-primary);
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }

        .user-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            filter: brightness(1.1);
        }

        .user-alert {
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1px solid;
            animation: alertSlideIn 0.5s ease-out;
        }

        .user-alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-color: rgba(239, 68, 68, 0.2);
        }

        .user-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .user-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .user-links a:hover {
            color: var(--primary-color);
        }

        .feature-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
            padding: 0.75rem;
            background: rgba(99, 102, 241, 0.1);
            border-radius: 0.5rem;
            font-size: 0.8rem;
            color: var(--text-muted);
        }
    </style>
</head>
<body>
    <div class="user-login-container">
        <div class="user-login-card">
            <div class="user-logo">
                <h1>👤 USER</h1>
                <div class="user-subtitle">CV Builder Portal</div>
                <div class="user-description">Create and manage your professional CV</div>
            </div>

            <?php if ($error): ?>
                <div class="user-alert user-alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="user-form-group">
                    <label for="email" class="user-form-label">Email Address</label>
                    <input type="email" id="email" name="email" class="user-form-control"
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                           placeholder="Enter your email" required>
                </div>

                <div class="user-form-group">
                    <label for="password" class="user-form-label">Password</label>
                    <input type="password" id="password" name="password" class="user-form-control"
                           placeholder="Enter your password" required>
                </div>

                <button type="submit" class="user-btn">Access Dashboard</button>
            </form>

            <div class="feature-badge">
                <span>📄</span>
                <span>Create Professional CVs</span>
            </div>

            <div class="user-links">
                <a href="register.php">Create New Account</a> |
                <a href="../index.php">← Back to Home</a> |
                <a href="../admin/login.php">Admin Login</a>
            </div>

            <div style="margin-top: 2rem; padding: 1rem; background: var(--bg-secondary); border-radius: 0.5rem; font-size: 0.8rem; color: var(--text-muted);">
                <strong>New User?</strong><br>
                Register for free to start creating professional CVs with our easy-to-use builder.
            </div>
        </div>
    </div>
</body>
</html>
