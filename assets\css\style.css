/* C<PERSON>er Dark Theme with Animations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-hover: #334155;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    --border-color: #334155;
    --border-hover: #475569;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--accent-color) 100%);
}

* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.03) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(139, 92, 246, 0.03) 50%, transparent 70%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(10px); }
    75% { transform: translateX(-5px) translateY(5px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

/* Header */
.header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    animation: slideDown 0.6s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    from {
        filter: drop-shadow(0 0 5px rgba(99, 102, 241, 0.3));
    }
    to {
        filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.6));
    }
}

.nav ul {
    list-style: none;
    display: flex;
    gap: 0.5rem;
}

.nav li {
    position: relative;
}

.nav a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    display: block;
    transform: translateY(0);
}

.nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav a:hover {
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav a:hover::before {
    left: 0;
}

.nav a:active {
    transform: translateY(0);
}

/* Main Content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
    position: relative;
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-hover);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.card-title {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
    animation: slideInLeft 0.5s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(even) {
    animation: slideInRight 0.5s ease-out;
    animation-fill-mode: both;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    position: relative;
    transition: color 0.3s ease;
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-control::placeholder {
    color: var(--text-muted);
    transition: opacity 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
    background: var(--bg-tertiary);
}

.form-control:focus::placeholder {
    opacity: 0.7;
}

.form-control:focus + .form-label {
    color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    animation: buttonPulse 2s ease-in-out infinite;
}

@keyframes buttonPulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4); }
    50% { box-shadow: 0 0 0 8px rgba(99, 102, 241, 0); }
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    filter: brightness(1.1);
}

.btn-primary:active {
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    border: 2px solid transparent;
}

.btn-success:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    filter: brightness(1.1);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-danger:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
    filter: brightness(1.1);
}

.btn-block {
    width: 100%;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid;
    position: relative;
    overflow: hidden;
    animation: alertSlideIn 0.5s ease-out;
}

@keyframes alertSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-color: rgba(239, 68, 68, 0.2);
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
}

.table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.table td {
    color: var(--text-secondary);
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: var(--bg-hover);
    transform: scale(1.01);
}

/* CV Preview */
.cv-preview {
    background: white;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.cv-header {
    text-align: center;
    border-bottom: 3px solid #667eea;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.cv-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.cv-section {
    margin-bottom: 2rem;
}

.cv-section h3 {
    color: #667eea;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav ul {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: statCardFloat 3s ease-in-out infinite;
}

@keyframes statCardFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    opacity: 0;
}

.stat-card:hover::before {
    opacity: 1;
    animation: shimmer 1s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: numberPulse 2s ease-in-out infinite;
}

@keyframes numberPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer */
.footer {
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    animation: footerGlow 3s ease-in-out infinite;
}

@keyframes footerGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Loading */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Hover effects for interactive elements */
.interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

/* Stagger animations for lists */
.stagger-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: staggerIn 0.5s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Particle effect */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    opacity: 0.3;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10%, 90% {
        opacity: 0.3;
    }
    50% {
        transform: translateY(-100px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Ripple effect animation */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Form focus states */
.form-group.focused .form-label {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.form-control.has-value {
    border-color: var(--success-color);
}

/* Enhanced hover states */
.card:hover .card-title {
    animation: titleGlow 1s ease-in-out;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .particles {
        display: none;
    }

    .stat-card {
        animation: none;
    }

    .btn {
        animation: none;
    }
}
