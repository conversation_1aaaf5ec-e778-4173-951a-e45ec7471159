// CV Builder JavaScript Functions with Modern Animations

let educationIndex = 0;
let experienceIndex = 0;
let skillIndex = 0;
let projectIndex = 0;
let certificationIndex = 0;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeCounters();
    initializeAnimations();
    initializeParticles();
    initializeScrollAnimations();
    initializeFormAnimations();
});

// Initialize counters based on existing entries
function initializeCounters() {
    const educationEntries = document.querySelectorAll('.education-entry');
    educationIndex = educationEntries.length;

    const experienceEntries = document.querySelectorAll('.experience-entry');
    experienceIndex = experienceEntries.length;

    const skillEntries = document.querySelectorAll('.skill-entry');
    skillIndex = skillEntries.length;

    const projectEntries = document.querySelectorAll('.project-entry');
    projectIndex = projectEntries.length;

    const certificationEntries = document.querySelectorAll('.certification-entry');
    certificationIndex = certificationEntries.length;
}

// Initialize modern animations
function initializeAnimations() {
    // Add stagger animation to navigation items
    const navItems = document.querySelectorAll('.nav li');
    navItems.forEach((item, index) => {
        item.classList.add('stagger-item');
        item.style.animationDelay = `${index * 0.1}s`;
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
    });

    // Add interactive class to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => btn.classList.add('interactive'));

    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.3}s`;
    });
}

// Create floating particles
function initializeParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles';
    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 20; i++) {
        createParticle(particlesContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');
    particle.className = 'particle';

    // Random position
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';

    // Random animation delay
    particle.style.animationDelay = Math.random() * 6 + 's';
    particle.style.animationDuration = (Math.random() * 3 + 3) + 's';

    container.appendChild(particle);

    // Remove and recreate particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.remove();
            createParticle(container);
        }
    }, 6000);
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    const animatedElements = document.querySelectorAll('.card, .stat-card, .form-group');
    animatedElements.forEach(el => {
        el.classList.add('fade-in');
        observer.observe(el);
    });
}

// Form animations
function initializeFormAnimations() {
    const formControls = document.querySelectorAll('.form-control');

    formControls.forEach(control => {
        // Focus animations
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            createRipple(this);
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        // Input validation animations
        control.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

// Create ripple effect
function createRipple(element) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(99, 102, 241, 0.3)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s linear';
    ripple.style.left = '50%';
    ripple.style.top = '50%';
    ripple.style.width = '20px';
    ripple.style.height = '20px';
    ripple.style.marginLeft = '-10px';
    ripple.style.marginTop = '-10px';
    ripple.style.pointerEvents = 'none';

    element.parentElement.style.position = 'relative';
    element.parentElement.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add Education Entry with Animation
function addEducation() {
    const container = document.getElementById('educationContainer');
    const entryDiv = document.createElement('div');
    entryDiv.className = 'education-entry';
    entryDiv.style.cssText = `
        border: 1px solid var(--border-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.75rem;
        background: var(--bg-secondary);
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
    `;

    entryDiv.innerHTML = `
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Degree *</label>
                <input type="text" name="education[${educationIndex}][degree]" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Year *</label>
                <input type="text" name="education[${educationIndex}][year]" class="form-control" placeholder="e.g., 2020-2024" required>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Institute *</label>
            <input type="text" name="education[${educationIndex}][institute]" class="form-control" required>
        </div>
        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
    `;

    container.appendChild(entryDiv);

    // Animate in
    setTimeout(() => {
        entryDiv.style.opacity = '1';
        entryDiv.style.transform = 'translateY(0)';
    }, 10);

    // Initialize form animations for new inputs
    initializeFormAnimationsForElement(entryDiv);
    educationIndex++;
}

// Initialize form animations for a specific element
function initializeFormAnimationsForElement(element) {
    const formControls = element.querySelectorAll('.form-control');

    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            createRipple(this);
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        control.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

// Add Work Experience Entry
function addWorkExperience() {
    const container = document.getElementById('experienceContainer');
    const entryHtml = `
        <div class="experience-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Job Title *</label>
                    <input type="text" name="experience[${experienceIndex}][job_title]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Company *</label>
                    <input type="text" name="experience[${experienceIndex}][company]" class="form-control" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Start Date *</label>
                    <input type="date" name="experience[${experienceIndex}][start_date]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">End Date</label>
                    <input type="date" name="experience[${experienceIndex}][end_date]" class="form-control">
                    <small>Leave empty if currently working</small>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <textarea name="experience[${experienceIndex}][description]" class="form-control" rows="3" placeholder="Describe your responsibilities and achievements..."></textarea>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    experienceIndex++;
}

// Add Skill Entry
function addSkill() {
    const container = document.getElementById('skillsContainer');
    const entryHtml = `
        <div class="skill-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Skill Name *</label>
                    <input type="text" name="skills[${skillIndex}][skill_name]" class="form-control" placeholder="e.g., JavaScript, Python, Project Management" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Skill Level *</label>
                    <select name="skills[${skillIndex}][skill_level]" class="form-control" required>
                        <option value="">Select Level</option>
                        <option value="Beginner">Beginner</option>
                        <option value="Intermediate">Intermediate</option>
                        <option value="Advanced">Advanced</option>
                        <option value="Expert">Expert</option>
                    </select>
                </div>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    skillIndex++;
}

// Add Project Entry
function addProject() {
    const container = document.getElementById('projectsContainer');
    const entryHtml = `
        <div class="project-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-group">
                <label class="form-label">Project Name *</label>
                <input type="text" name="projects[${projectIndex}][project_name]" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Description *</label>
                <textarea name="projects[${projectIndex}][description]" class="form-control" rows="3" placeholder="Describe the project, technologies used, and your role..." required></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">GitHub Link</label>
                <input type="url" name="projects[${projectIndex}][github_link]" class="form-control" placeholder="https://github.com/username/project">
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    projectIndex++;
}

// Add Certification Entry
function addCertification() {
    const container = document.getElementById('certificationsContainer');
    const entryHtml = `
        <div class="certification-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-group">
                <label class="form-label">Certification Name *</label>
                <input type="text" name="certifications[${certificationIndex}][certification_name]" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Issuing Organization</label>
                <input type="text" name="certifications[${certificationIndex}][issuing_organization]" class="form-control">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Issue Date</label>
                    <input type="date" name="certifications[${certificationIndex}][issue_date]" class="form-control">
                </div>
                <div class="form-group">
                    <label class="form-label">Expiry Date</label>
                    <input type="date" name="certifications[${certificationIndex}][expiry_date]" class="form-control">
                </div>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    certificationIndex++;
}

// Remove Entry
function removeEntry(button) {
    const entry = button.closest('.education-entry, .experience-entry, .skill-entry, .project-entry, .certification-entry');
    if (entry) {
        entry.remove();
    }
}

// Form Validation
function validateForm() {
    const form = document.getElementById('cvForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#e9ecef';
        }
    });

    if (!isValid) {
        alert('Please fill in all required fields.');
        return false;
    }

    return true;
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Auto-save functionality (optional)
function autoSave() {
    const form = document.getElementById('cvForm');
    if (form) {
        const formData = new FormData(form);
        // Convert FormData to object for localStorage
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        localStorage.setItem('cvFormData', JSON.stringify(data));
        console.log('Auto-saving form data...');
    }
}

// Set up auto-save every 30 seconds
setInterval(autoSave, 30000);

// Preview image before upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Add event listener for image upload
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('user_image');
    if (imageInput) {
        imageInput.addEventListener('change', function() {
            previewImage(this);
        });
    }
});

// Form submission with loading state
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('cvForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return;
            }

            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Processing...';
            }
        });
    }
});
