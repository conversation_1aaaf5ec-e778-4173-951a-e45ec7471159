// CV Builder JavaScript Functions

let educationIndex = 0;
let experienceIndex = 0;
let skillIndex = 0;
let projectIndex = 0;
let certificationIndex = 0;

// Initialize counters based on existing entries
document.addEventListener('DOMContentLoaded', function() {
    // Count existing education entries
    const educationEntries = document.querySelectorAll('.education-entry');
    educationIndex = educationEntries.length;
    
    // Count existing experience entries
    const experienceEntries = document.querySelectorAll('.experience-entry');
    experienceIndex = experienceEntries.length;
    
    // Count existing skill entries
    const skillEntries = document.querySelectorAll('.skill-entry');
    skillIndex = skillEntries.length;
    
    // Count existing project entries
    const projectEntries = document.querySelectorAll('.project-entry');
    projectIndex = projectEntries.length;
    
    // Count existing certification entries
    const certificationEntries = document.querySelectorAll('.certification-entry');
    certificationIndex = certificationEntries.length;
});

// Add Education Entry
function addEducation() {
    const container = document.getElementById('educationContainer');
    const entryHtml = `
        <div class="education-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Degree *</label>
                    <input type="text" name="education[${educationIndex}][degree]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Year *</label>
                    <input type="text" name="education[${educationIndex}][year]" class="form-control" placeholder="e.g., 2020-2024" required>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Institute *</label>
                <input type="text" name="education[${educationIndex}][institute]" class="form-control" required>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    educationIndex++;
}

// Add Work Experience Entry
function addWorkExperience() {
    const container = document.getElementById('experienceContainer');
    const entryHtml = `
        <div class="experience-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Job Title *</label>
                    <input type="text" name="experience[${experienceIndex}][job_title]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Company *</label>
                    <input type="text" name="experience[${experienceIndex}][company]" class="form-control" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Start Date *</label>
                    <input type="date" name="experience[${experienceIndex}][start_date]" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">End Date</label>
                    <input type="date" name="experience[${experienceIndex}][end_date]" class="form-control">
                    <small>Leave empty if currently working</small>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <textarea name="experience[${experienceIndex}][description]" class="form-control" rows="3" placeholder="Describe your responsibilities and achievements..."></textarea>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    experienceIndex++;
}

// Add Skill Entry
function addSkill() {
    const container = document.getElementById('skillsContainer');
    const entryHtml = `
        <div class="skill-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Skill Name *</label>
                    <input type="text" name="skills[${skillIndex}][skill_name]" class="form-control" placeholder="e.g., JavaScript, Python, Project Management" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Skill Level *</label>
                    <select name="skills[${skillIndex}][skill_level]" class="form-control" required>
                        <option value="">Select Level</option>
                        <option value="Beginner">Beginner</option>
                        <option value="Intermediate">Intermediate</option>
                        <option value="Advanced">Advanced</option>
                        <option value="Expert">Expert</option>
                    </select>
                </div>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    skillIndex++;
}

// Add Project Entry
function addProject() {
    const container = document.getElementById('projectsContainer');
    const entryHtml = `
        <div class="project-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-group">
                <label class="form-label">Project Name *</label>
                <input type="text" name="projects[${projectIndex}][project_name]" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Description *</label>
                <textarea name="projects[${projectIndex}][description]" class="form-control" rows="3" placeholder="Describe the project, technologies used, and your role..." required></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">GitHub Link</label>
                <input type="url" name="projects[${projectIndex}][github_link]" class="form-control" placeholder="https://github.com/username/project">
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    projectIndex++;
}

// Add Certification Entry
function addCertification() {
    const container = document.getElementById('certificationsContainer');
    const entryHtml = `
        <div class="certification-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
            <div class="form-group">
                <label class="form-label">Certification Name *</label>
                <input type="text" name="certifications[${certificationIndex}][certification_name]" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Issuing Organization</label>
                <input type="text" name="certifications[${certificationIndex}][issuing_organization]" class="form-control">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Issue Date</label>
                    <input type="date" name="certifications[${certificationIndex}][issue_date]" class="form-control">
                </div>
                <div class="form-group">
                    <label class="form-label">Expiry Date</label>
                    <input type="date" name="certifications[${certificationIndex}][expiry_date]" class="form-control">
                </div>
            </div>
            <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', entryHtml);
    certificationIndex++;
}

// Remove Entry
function removeEntry(button) {
    const entry = button.closest('.education-entry, .experience-entry, .skill-entry, .project-entry, .certification-entry');
    if (entry) {
        entry.remove();
    }
}

// Form Validation
function validateForm() {
    const form = document.getElementById('cvForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#e9ecef';
        }
    });
    
    if (!isValid) {
        alert('Please fill in all required fields.');
        return false;
    }
    
    return true;
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Auto-save functionality (optional)
function autoSave() {
    const formData = new FormData(document.getElementById('cvForm'));
    // You can implement auto-save to localStorage or send AJAX request
    console.log('Auto-saving form data...');
}

// Set up auto-save every 30 seconds
setInterval(autoSave, 30000);

// Preview image before upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Add event listener for image upload
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('user_image');
    if (imageInput) {
        imageInput.addEventListener('change', function() {
            previewImage(this);
        });
    }
});

// Form submission with loading state
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('cvForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return;
            }
            
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Processing...';
            }
        });
    }
});
