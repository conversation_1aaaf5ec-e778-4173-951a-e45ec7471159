<?php
// Demo Admin CVs Management Page
session_start();

// Simulate admin login for demo
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin User';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_type'] = 'admin';

// Mock CVs data
$cvs = [
    [
        'id' => 1, 'full_name' => '<PERSON>', 'user_name' => '<PERSON>', 'email' => '<EMAIL>', 'user_id' => 1,
        'education_count' => 2, 'experience_count' => 3, 'skills_count' => 8, 'projects_count' => 2, 'certifications_count' => 1,
        'user_image' => 'profile1.jpg', 'created_at' => '2024-01-15 11:00:00', 'updated_at' => '2024-01-15 11:30:00'
    ],
    [
        'id' => 2, 'full_name' => '<PERSON>', 'user_name' => '<PERSON>', 'email' => '<EMAIL>', 'user_id' => 2,
        'education_count' => 1, 'experience_count' => 2, 'skills_count' => 6, 'projects_count' => 3, 'certifications_count' => 2,
        'user_image' => '', 'created_at' => '2024-01-14 15:30:00', 'updated_at' => '2024-01-14 16:00:00'
    ],
    [
        'id' => 3, 'full_name' => 'Mike Johnson', 'user_name' => '<PERSON> <PERSON>', 'email' => '<EMAIL>', 'user_id' => 3,
        'education_count' => 1, 'experience_count' => 4, 'skills_count' => 10, 'projects_count' => 1, 'certifications_count' => 0,
        'user_image' => 'profile2.jpg', 'created_at' => '2024-01-13 10:45:00', 'updated_at' => '2024-01-13 11:15:00'
    ],
    [
        'id' => 4, 'full_name' => 'Sarah Wilson', 'user_name' => 'Sarah Wilson', 'email' => '<EMAIL>', 'user_id' => 4,
        'education_count' => 2, 'experience_count' => 1, 'skills_count' => 5, 'projects_count' => 4, 'certifications_count' => 1,
        'user_image' => '', 'created_at' => '2024-01-12 17:20:00', 'updated_at' => '2024-01-12 17:45:00'
    ],
    [
        'id' => 5, 'full_name' => 'Emily Davis', 'user_name' => 'Emily Davis', 'email' => '<EMAIL>', 'user_id' => 6,
        'education_count' => 1, 'experience_count' => 2, 'skills_count' => 7, 'projects_count' => 2, 'certifications_count' => 3,
        'user_image' => 'profile3.jpg', 'created_at' => '2024-01-10 14:00:00', 'updated_at' => '2024-01-10 14:30:00'
    ],
    [
        'id' => 6, 'full_name' => 'Lisa Anderson', 'user_name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'user_id' => 8,
        'education_count' => 3, 'experience_count' => 5, 'skills_count' => 12, 'projects_count' => 1, 'certifications_count' => 2,
        'user_image' => '', 'created_at' => '2024-01-08 16:10:00', 'updated_at' => '2024-01-08 16:40:00'
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All CVs - CV Builder Admin</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="admin_demo.php">Admin Dashboard</a></li>
                    <li><a href="admin_users_demo.php">Manage Users</a></li>
                    <li><a href="admin_cvs_demo.php">View All CVs</a></li>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">All CVs</h1>
                    <p>View and manage all created CVs</p>
                </div>

                <!-- CV Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($cvs); ?></div>
                        <div class="stat-label">Total CVs</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return strtotime($cv['created_at']) > strtotime('-30 days'); })); ?></div>
                        <div class="stat-label">Created This Month</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return !empty($cv['user_image']); })); ?></div>
                        <div class="stat-label">With Photos</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return $cv['experience_count'] > 0; })); ?></div>
                        <div class="stat-label">With Experience</div>
                    </div>
                </div>

                <!-- CVs Table -->
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>CV ID</th>
                                <th>CV Name</th>
                                <th>User</th>
                                <th>Email</th>
                                <th>Sections</th>
                                <th>Photo</th>
                                <th>Created</th>
                                <th>Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cvs as $cv): ?>
                                <tr>
                                    <td><?php echo $cv['id']; ?></td>
                                    <td><?php echo htmlspecialchars($cv['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($cv['user_name']); ?></td>
                                    <td><?php echo htmlspecialchars($cv['email']); ?></td>
                                    <td>
                                        <div style="font-size: 0.8rem;">
                                            <?php
                                            $sections = [];
                                            if ($cv['education_count'] > 0) $sections[] = "Edu({$cv['education_count']})";
                                            if ($cv['experience_count'] > 0) $sections[] = "Exp({$cv['experience_count']})";
                                            if ($cv['skills_count'] > 0) $sections[] = "Skills({$cv['skills_count']})";
                                            if ($cv['projects_count'] > 0) $sections[] = "Proj({$cv['projects_count']})";
                                            if ($cv['certifications_count'] > 0) $sections[] = "Cert({$cv['certifications_count']})";
                                            echo implode(', ', $sections) ?: 'None';
                                            ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($cv['user_image']): ?>
                                            <span style="color: green;">✓</span>
                                        <?php else: ?>
                                            <span style="color: #999;">✗</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($cv['created_at'])); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($cv['updated_at'])); ?></td>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                            <a href="sample_cv.html" class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                               View CV
                                            </a>
                                            <a href="#" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                               View User
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Back to Dashboard -->
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="admin_demo.php" class="btn btn-primary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
