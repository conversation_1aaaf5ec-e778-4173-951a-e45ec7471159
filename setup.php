<?php
// CV Builder Setup Script
// This script helps set up the database and initial configuration

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// Database configuration
$db_config = [
    'host' => 'localhost',
    'user' => 'root',
    'pass' => '',
    'name' => 'cv_builder'
];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // Database connection test
        $db_config['host'] = $_POST['db_host'];
        $db_config['user'] = $_POST['db_user'];
        $db_config['pass'] = $_POST['db_pass'];
        $db_config['name'] = $_POST['db_name'];
        
        try {
            $dsn = "mysql:host={$db_config['host']}";
            $pdo = new PDO($dsn, $db_config['user'], $db_config['pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_config['name']}`");
            $pdo->exec("USE `{$db_config['name']}`");
            
            $success = 'Database connection successful! Database created/selected.';
            $step = 2;
        } catch (PDOException $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Create tables
        try {
            $dsn = "mysql:host={$db_config['host']};dbname={$db_config['name']}";
            $pdo = new PDO($dsn, $db_config['user'], $db_config['pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Read and execute schema
            $schema = file_get_contents('database/schema.sql');
            $statements = explode(';', $schema);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            $success = 'Database tables created successfully!';
            $step = 3;
        } catch (PDOException $e) {
            $error = 'Error creating tables: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Builder Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .step {
            text-align: center;
            margin-bottom: 20px;
        }
        .step-indicator {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            line-height: 30px;
            margin: 0 10px;
        }
        .step-indicator.inactive {
            background: #ccc;
        }
        .requirements {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .requirements h3 {
            margin-top: 0;
            color: #667eea;
        }
        .requirements ul {
            margin: 10px 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CV Builder Setup</h1>
        
        <!-- Step Indicator -->
        <div class="step">
            <span class="step-indicator <?php echo $step >= 1 ? '' : 'inactive'; ?>">1</span>
            <span class="step-indicator <?php echo $step >= 2 ? '' : 'inactive'; ?>">2</span>
            <span class="step-indicator <?php echo $step >= 3 ? '' : 'inactive'; ?>">3</span>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if ($step == 1): ?>
            <h2>Step 1: Database Configuration</h2>
            
            <div class="requirements">
                <h3>Requirements:</h3>
                <ul>
                    <li>PHP 7.4 or higher</li>
                    <li>MySQL 5.7 or higher</li>
                    <li>PDO MySQL extension enabled</li>
                    <li>Write permissions for uploads directory</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="db_host">Database Host:</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="db_user">Database Username:</label>
                    <input type="text" id="db_user" name="db_user" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="db_pass">Database Password:</label>
                    <input type="password" id="db_pass" name="db_pass">
                </div>
                
                <div class="form-group">
                    <label for="db_name">Database Name:</label>
                    <input type="text" id="db_name" name="db_name" value="cv_builder" required>
                </div>
                
                <button type="submit" class="btn">Test Connection & Create Database</button>
            </form>
            
        <?php elseif ($step == 2): ?>
            <h2>Step 2: Create Database Tables</h2>
            <p>Database connection successful! Now let's create the required tables.</p>
            
            <form method="POST">
                <input type="hidden" name="step" value="2">
                <button type="submit" class="btn">Create Tables</button>
            </form>
            
        <?php elseif ($step == 3): ?>
            <h2>Step 3: Setup Complete!</h2>
            
            <div class="alert alert-success">
                <strong>Congratulations!</strong> CV Builder has been set up successfully.
            </div>
            
            <div class="requirements">
                <h3>Default Admin Account:</h3>
                <ul>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Password:</strong> admin123</li>
                </ul>
                <p><strong>Important:</strong> Please change the admin password after first login!</p>
            </div>
            
            <div class="requirements">
                <h3>Next Steps:</h3>
                <ul>
                    <li>Delete or rename this setup.php file for security</li>
                    <li>Configure your web server to point to this directory</li>
                    <li>Ensure the uploads/ directory has write permissions</li>
                    <li>Consider setting up SSL/HTTPS for production</li>
                </ul>
            </div>
            
            <a href="index.php" class="btn" style="text-decoration: none; display: block; text-align: center;">
                Go to CV Builder
            </a>
        <?php endif; ?>
    </div>
</body>
</html>
