<?php
// Demo Admin View User Page
session_start();

// Simulate admin login for demo
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin User';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_type'] = 'admin';

// Get user ID from URL or default to 1
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 1;

// Mock user data
$users = [
    1 => ['id' => 1, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'user_type' => 'user', 'created_at' => '2024-01-15 10:30:00', 'updated_at' => '2024-01-15 11:00:00'],
    2 => ['id' => 2, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'user_type' => 'user', 'created_at' => '2024-01-14 14:20:00', 'updated_at' => '2024-01-14 15:30:00'],
    3 => ['id' => 3, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'user_type' => 'user', 'created_at' => '2024-01-13 09:15:00', 'updated_at' => '2024-01-13 10:45:00'],
    4 => ['id' => 4, 'full_name' => 'Sarah Wilson', 'email' => '<EMAIL>', 'user_type' => 'user', 'created_at' => '2024-01-12 16:45:00', 'updated_at' => '2024-01-12 17:20:00'],
    5 => ['id' => 5, 'full_name' => 'David Brown', 'email' => '<EMAIL>', 'user_type' => 'user', 'created_at' => '2024-01-11 11:30:00', 'updated_at' => '2024-01-11 11:30:00']
];

$user = isset($users[$user_id]) ? $users[$user_id] : $users[1];

// Mock CV data
$cv_data = [
    'id' => 1,
    'full_name' => $user['full_name'],
    'email' => $user['email'],
    'contact' => '+1 (555) 123-4567',
    'user_image' => 'profile.jpg',
    'created_at' => '2024-01-15 11:00:00',
    'updated_at' => '2024-01-15 11:30:00'
];

$education = [
    ['degree' => 'Bachelor of Computer Science', 'institute' => 'University of Technology', 'year' => '2016-2020'],
    ['degree' => 'Master of Software Engineering', 'institute' => 'Tech Institute', 'year' => '2020-2022']
];

$work_experience = [
    ['job_title' => 'Senior Developer', 'company' => 'Tech Corp', 'start_date' => '2022-01-01', 'end_date' => null, 'description' => 'Leading development team and architecting solutions'],
    ['job_title' => 'Junior Developer', 'company' => 'StartUp Inc', 'start_date' => '2020-06-01', 'end_date' => '2021-12-31', 'description' => 'Full-stack web development']
];

$skills = [
    ['skill_name' => 'JavaScript', 'skill_level' => 'Expert'],
    ['skill_name' => 'PHP', 'skill_level' => 'Advanced'],
    ['skill_name' => 'React', 'skill_level' => 'Advanced'],
    ['skill_name' => 'MySQL', 'skill_level' => 'Intermediate']
];

$projects = [
    ['project_name' => 'CV Builder', 'description' => 'A comprehensive web-based CV builder using PHP and MySQL', 'github_link' => 'https://github.com/user/cv-builder'],
    ['project_name' => 'E-commerce Platform', 'description' => 'Full-featured online store with payment integration', 'github_link' => 'https://github.com/user/ecommerce']
];

$certifications = [
    ['certification_name' => 'AWS Certified Developer', 'issuing_organization' => 'Amazon Web Services', 'issue_date' => '2023-03-01', 'expiry_date' => '2026-03-01']
];

$has_cv = true;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User - <?php echo htmlspecialchars($user['full_name']); ?> - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="../admin_demo.php">Admin Dashboard</a></li>
                    <li><a href="../admin_users_demo.php">Manage Users</a></li>
                    <li><a href="../admin_cvs_demo.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- User Information -->
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">User Details</h1>
                    <p>Complete information for <?php echo htmlspecialchars($user['full_name']); ?></p>
                </div>

                <!-- User Basic Info -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h3>Personal Information</h3>
                        <p><strong>User ID:</strong> <?php echo $user['id']; ?></p>
                        <p><strong>Full Name:</strong> <?php echo htmlspecialchars($user['full_name']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                        <p><strong>User Type:</strong> <?php echo ucfirst($user['user_type']); ?></p>
                    </div>
                    
                    <div>
                        <h3>Account Activity</h3>
                        <p><strong>Registered:</strong> <?php echo date('M d, Y H:i', strtotime($user['created_at'])); ?></p>
                        <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($user['updated_at'])); ?></p>
                        <p><strong>CV Status:</strong> 
                            <span style="color: var(--success-color); font-weight: bold;">✓ CV Created</span>
                        </p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 2rem;">
                    <a href="../admin_users_demo.php" class="btn btn-secondary">Back to Users</a>
                    <a href="../sample_cv.html" class="btn btn-primary" target="_blank">View User's CV</a>
                    <a href="../sample_cv.html" class="btn btn-success" target="_blank">Preview CV</a>
                </div>

                <!-- CV Summary -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">CV Summary</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                        <!-- CV Basic Info -->
                        <div>
                            <h4>CV Information</h4>
                            <p><strong>CV Name:</strong> <?php echo htmlspecialchars($cv_data['full_name']); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($cv_data['email']); ?></p>
                            <p><strong>Contact:</strong> <?php echo htmlspecialchars($cv_data['contact']); ?></p>
                            <p><strong>Profile Photo:</strong> <span style="color: var(--success-color);">✓ Uploaded</span></p>
                        </div>
                        
                        <!-- CV Statistics -->
                        <div>
                            <h4>CV Sections</h4>
                            <p><strong>Education:</strong> <?php echo count($education); ?> entries</p>
                            <p><strong>Work Experience:</strong> <?php echo count($work_experience); ?> entries</p>
                            <p><strong>Skills:</strong> <?php echo count($skills); ?> entries</p>
                            <p><strong>Projects:</strong> <?php echo count($projects); ?> entries</p>
                            <p><strong>Certifications:</strong> <?php echo count($certifications); ?> entries</p>
                        </div>
                        
                        <!-- CV Dates -->
                        <div>
                            <h4>CV Timeline</h4>
                            <p><strong>CV Created:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['created_at'])); ?></p>
                            <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['updated_at'])); ?></p>
                            <p><strong>Completeness:</strong> 100% (5/5 sections)</p>
                        </div>
                    </div>
                </div>

                <!-- Detailed CV Sections -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Education (<?php echo count($education); ?> entries)</h3>
                    </div>
                    <div class="table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Degree</th>
                                    <th>Institute</th>
                                    <th>Year</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($education as $edu): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($edu['degree']); ?></td>
                                        <td><?php echo htmlspecialchars($edu['institute']); ?></td>
                                        <td><?php echo htmlspecialchars($edu['year']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Work Experience (<?php echo count($work_experience); ?> entries)</h3>
                    </div>
                    <div class="table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Job Title</th>
                                    <th>Company</th>
                                    <th>Duration</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($work_experience as $exp): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($exp['job_title']); ?></td>
                                        <td><?php echo htmlspecialchars($exp['company']); ?></td>
                                        <td>
                                            <?php echo date('M Y', strtotime($exp['start_date'])); ?> - 
                                            <?php echo $exp['end_date'] ? date('M Y', strtotime($exp['end_date'])) : 'Present'; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($exp['description']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Skills (<?php echo count($skills); ?> entries)</h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <?php foreach ($skills as $skill): ?>
                            <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 0.5rem; border: 1px solid var(--border-color);">
                                <div style="font-weight: 600; margin-bottom: 0.5rem;"><?php echo htmlspecialchars($skill['skill_name']); ?></div>
                                <div style="color: var(--primary-color); font-size: 0.9rem;"><?php echo htmlspecialchars($skill['skill_level']); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
