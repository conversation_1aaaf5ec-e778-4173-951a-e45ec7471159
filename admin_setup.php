<?php
// Admin Setup Script - Run this to ensure admin user exists with correct password

require_once 'config/database.php';

try {
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT id, email FROM users WHERE email = ? AND user_type = 'admin'");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    // Hash for password "admin123"
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    
    if ($admin) {
        // Update existing admin password
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ? AND user_type = 'admin'");
        $stmt->execute([$password_hash, '<EMAIL>']);
        echo "✅ Admin password updated successfully!<br>";
    } else {
        // Create new admin user
        $stmt = $pdo->prepare("INSERT INTO users (full_name, email, password, user_type) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Admin User', '<EMAIL>', $password_hash, 'admin']);
        echo "✅ Admin user created successfully!<br>";
    }
    
    // Verify the password works
    $stmt = $pdo->prepare("SELECT password FROM users WHERE email = ? AND user_type = 'admin'");
    $stmt->execute(['<EMAIL>']);
    $stored_hash = $stmt->fetchColumn();
    
    if (password_verify('admin123', $stored_hash)) {
        echo "✅ Password verification successful!<br>";
        echo "<br><strong>Admin Login Credentials:</strong><br>";
        echo "Email: <EMAIL><br>";
        echo "Password: admin123<br>";
        echo "<br><a href='admin/login.php'>🔗 Go to Admin Login</a><br>";
        echo "<a href='index.php'>🏠 Back to Homepage</a>";
    } else {
        echo "❌ Password verification failed!";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Please make sure the database is set up correctly.<br>";
    echo "<a href='setup.php'>🔧 Run Database Setup</a>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - CV Builder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc3545;
            text-align: center;
        }
        a {
            color: #007bff;
            text-decoration: none;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        a:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Admin Setup Complete</h1>
    </div>
</body>
</html>
