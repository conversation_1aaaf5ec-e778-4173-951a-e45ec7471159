<?php
require_once 'includes/functions.php';
requireLogin();

// Get existing CV data if available
$cv_data = getUserCVData($_SESSION['user_id']);
$education = $cv_data ? getUserEducation($cv_data['id']) : [];
$work_experience = $cv_data ? getUserWorkExperience($cv_data['id']) : [];
$skills = $cv_data ? getUserSkills($cv_data['id']) : [];
$projects = $cv_data ? getUserProjects($cv_data['id']) : [];
$certifications = $cv_data ? getUserCertifications($cv_data['id']) : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $cv_data ? 'Edit' : 'Create'; ?> CV - CV Builder</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="cv_form.php">Edit CV</a></li>
                    <?php if ($cv_data): ?>
                        <li><a href="cv_preview.php">Preview CV</a></li>
                    <?php endif; ?>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title"><?php echo $cv_data ? 'Edit Your CV' : 'Create Your CV'; ?></h1>
                    <p>Fill in your information to create a professional CV</p>
                </div>

                <?php displayMessages(); ?>

                <form method="POST" action="process_cv.php" enctype="multipart/form-data" id="cvForm">
                    <!-- Personal Information -->
                    <div class="card" id="personal">
                        <div class="card-header">
                            <h3 class="card-title">Personal Information</h3>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" id="full_name" name="full_name" class="form-control"
                                       value="<?php echo $cv_data ? htmlspecialchars($cv_data['full_name']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" id="email" name="email" class="form-control"
                                       value="<?php echo $cv_data ? htmlspecialchars($cv_data['email']) : ''; ?>" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="contact" class="form-label">Contact Number *</label>
                                <input type="tel" id="contact" name="contact" class="form-control"
                                       value="<?php echo $cv_data ? htmlspecialchars($cv_data['contact']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="user_image" class="form-label">Profile Photo</label>
                                <input type="file" id="user_image" name="user_image" class="form-control" accept="image/*">
                                <?php if ($cv_data && $cv_data['user_image']): ?>
                                    <small>Current photo: <?php echo htmlspecialchars($cv_data['user_image']); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Education -->
                    <div class="card" id="education">
                        <div class="card-header">
                            <h3 class="card-title">Education</h3>
                            <button type="button" class="btn btn-secondary" onclick="addEducation()">Add Education</button>
                        </div>

                        <div id="educationContainer">
                            <?php if (!empty($education)): ?>
                                <?php foreach ($education as $index => $edu): ?>
                                    <div class="education-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="form-label">Degree *</label>
                                                <input type="text" name="education[<?php echo $index; ?>][degree]" class="form-control"
                                                       value="<?php echo htmlspecialchars($edu['degree']); ?>" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">Year *</label>
                                                <input type="text" name="education[<?php echo $index; ?>][year]" class="form-control"
                                                       value="<?php echo htmlspecialchars($edu['year']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Institute *</label>
                                            <input type="text" name="education[<?php echo $index; ?>][institute]" class="form-control"
                                                   value="<?php echo htmlspecialchars($edu['institute']); ?>" required>
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Work Experience -->
                    <div class="card" id="experience">
                        <div class="card-header">
                            <h3 class="card-title">Work Experience</h3>
                            <button type="button" class="btn btn-secondary" onclick="addWorkExperience()">Add Experience</button>
                        </div>

                        <div id="experienceContainer">
                            <?php if (!empty($work_experience)): ?>
                                <?php foreach ($work_experience as $index => $exp): ?>
                                    <div class="experience-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="form-label">Job Title *</label>
                                                <input type="text" name="experience[<?php echo $index; ?>][job_title]" class="form-control"
                                                       value="<?php echo htmlspecialchars($exp['job_title']); ?>" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">Company *</label>
                                                <input type="text" name="experience[<?php echo $index; ?>][company]" class="form-control"
                                                       value="<?php echo htmlspecialchars($exp['company']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="form-label">Start Date *</label>
                                                <input type="date" name="experience[<?php echo $index; ?>][start_date]" class="form-control"
                                                       value="<?php echo $exp['start_date']; ?>" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">End Date</label>
                                                <input type="date" name="experience[<?php echo $index; ?>][end_date]" class="form-control"
                                                       value="<?php echo $exp['end_date']; ?>">
                                                <small>Leave empty if currently working</small>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Description</label>
                                            <textarea name="experience[<?php echo $index; ?>][description]" class="form-control" rows="3"><?php echo htmlspecialchars($exp['description']); ?></textarea>
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Skills -->
                    <div class="card" id="skills">
                        <div class="card-header">
                            <h3 class="card-title">Skills & Technologies</h3>
                            <button type="button" class="btn btn-secondary" onclick="addSkill()">Add Skill</button>
                        </div>

                        <div id="skillsContainer">
                            <?php if (!empty($skills)): ?>
                                <?php foreach ($skills as $index => $skill): ?>
                                    <div class="skill-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="form-label">Skill Name *</label>
                                                <input type="text" name="skills[<?php echo $index; ?>][skill_name]" class="form-control"
                                                       value="<?php echo htmlspecialchars($skill['skill_name']); ?>" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">Skill Level *</label>
                                                <select name="skills[<?php echo $index; ?>][skill_level]" class="form-control" required>
                                                    <option value="Beginner" <?php echo $skill['skill_level'] == 'Beginner' ? 'selected' : ''; ?>>Beginner</option>
                                                    <option value="Intermediate" <?php echo $skill['skill_level'] == 'Intermediate' ? 'selected' : ''; ?>>Intermediate</option>
                                                    <option value="Advanced" <?php echo $skill['skill_level'] == 'Advanced' ? 'selected' : ''; ?>>Advanced</option>
                                                    <option value="Expert" <?php echo $skill['skill_level'] == 'Expert' ? 'selected' : ''; ?>>Expert</option>
                                                </select>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Projects -->
                    <div class="card" id="projects">
                        <div class="card-header">
                            <h3 class="card-title">Projects</h3>
                            <button type="button" class="btn btn-secondary" onclick="addProject()">Add Project</button>
                        </div>

                        <div id="projectsContainer">
                            <?php if (!empty($projects)): ?>
                                <?php foreach ($projects as $index => $project): ?>
                                    <div class="project-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                        <div class="form-group">
                                            <label class="form-label">Project Name *</label>
                                            <input type="text" name="projects[<?php echo $index; ?>][project_name]" class="form-control"
                                                   value="<?php echo htmlspecialchars($project['project_name']); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Description *</label>
                                            <textarea name="projects[<?php echo $index; ?>][description]" class="form-control" rows="3" required><?php echo htmlspecialchars($project['description']); ?></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">GitHub Link</label>
                                            <input type="url" name="projects[<?php echo $index; ?>][github_link]" class="form-control"
                                                   value="<?php echo htmlspecialchars($project['github_link']); ?>">
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Certifications -->
                    <div class="card" id="certifications">
                        <div class="card-header">
                            <h3 class="card-title">Certifications</h3>
                            <button type="button" class="btn btn-secondary" onclick="addCertification()">Add Certification</button>
                        </div>

                        <div id="certificationsContainer">
                            <?php if (!empty($certifications)): ?>
                                <?php foreach ($certifications as $index => $cert): ?>
                                    <div class="certification-entry" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                        <div class="form-group">
                                            <label class="form-label">Certification Name *</label>
                                            <input type="text" name="certifications[<?php echo $index; ?>][certification_name]" class="form-control"
                                                   value="<?php echo htmlspecialchars($cert['certification_name']); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Issuing Organization</label>
                                            <input type="text" name="certifications[<?php echo $index; ?>][issuing_organization]" class="form-control"
                                                   value="<?php echo htmlspecialchars($cert['issuing_organization']); ?>">
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="form-label">Issue Date</label>
                                                <input type="date" name="certifications[<?php echo $index; ?>][issue_date]" class="form-control"
                                                       value="<?php echo $cert['issue_date']; ?>">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">Expiry Date</label>
                                                <input type="date" name="certifications[<?php echo $index; ?>][expiry_date]" class="form-control"
                                                       value="<?php echo $cert['expiry_date']; ?>">
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="removeEntry(this)">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" class="btn btn-primary btn-block" style="max-width: 300px;">
                            <?php echo $cv_data ? 'Update CV' : 'Create CV'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
</body>
</html>
