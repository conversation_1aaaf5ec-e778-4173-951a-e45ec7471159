<?php
require_once 'includes/functions.php';
requireLogin();

// Get user's CV data
$cv_data = getUserCVData($_SESSION['user_id']);
$has_cv = !empty($cv_data);

if ($has_cv) {
    $education = getUserEducation($cv_data['id']);
    $work_experience = getUserWorkExperience($cv_data['id']);
    $skills = getUserSkills($cv_data['id']);
    $projects = getUserProjects($cv_data['id']);
    $certifications = getUserCertifications($cv_data['id']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - CV Builder</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="cv_form.php"><?php echo $has_cv ? 'Edit CV' : 'Create CV'; ?></a></li>
                    <?php if ($has_cv): ?>
                        <li><a href="cv_preview.php">Preview CV</a></li>
                    <?php endif; ?>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?>!</h1>
                    <p>Manage your CV and track your progress</p>
                </div>

                <?php displayMessages(); ?>

                <!-- Dashboard Stats -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $has_cv ? '1' : '0'; ?></div>
                        <div class="stat-label">CV Created</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $has_cv ? count($education) : '0'; ?></div>
                        <div class="stat-label">Education Entries</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $has_cv ? count($work_experience) : '0'; ?></div>
                        <div class="stat-label">Work Experience</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $has_cv ? count($skills) : '0'; ?></div>
                        <div class="stat-label">Skills Listed</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <?php if (!$has_cv): ?>
                            <a href="cv_form.php" class="btn btn-primary">Create Your First CV</a>
                        <?php else: ?>
                            <a href="cv_form.php" class="btn btn-primary">Edit CV Information</a>
                            <a href="cv_preview.php" class="btn btn-secondary">Preview CV</a>
                            <a href="download_cv.php" class="btn btn-success">Download PDF</a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($has_cv): ?>
                <!-- CV Summary -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Your CV Summary</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <!-- Personal Info -->
                        <div>
                            <h4>Personal Information</h4>
                            <p><strong>Name:</strong> <?php echo htmlspecialchars($cv_data['full_name']); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($cv_data['email']); ?></p>
                            <p><strong>Contact:</strong> <?php echo htmlspecialchars($cv_data['contact']); ?></p>
                            <?php if ($cv_data['user_image']): ?>
                                <p><strong>Photo:</strong> Uploaded</p>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div>
                            <h4>Recent Activity</h4>
                            <p><strong>CV Created:</strong> <?php echo date('M d, Y', strtotime($cv_data['created_at'])); ?></p>
                            <p><strong>Last Updated:</strong> <?php echo date('M d, Y', strtotime($cv_data['updated_at'])); ?></p>
                            <p><strong>Total Sections:</strong> 
                                <?php 
                                $sections = 0;
                                if (!empty($education)) $sections++;
                                if (!empty($work_experience)) $sections++;
                                if (!empty($skills)) $sections++;
                                if (!empty($projects)) $sections++;
                                if (!empty($certifications)) $sections++;
                                echo $sections . ' of 5';
                                ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Section Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">CV Sections Status</h3>
                    </div>
                    
                    <div class="table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Section</th>
                                    <th>Status</th>
                                    <th>Entries</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Education</td>
                                    <td><?php echo !empty($education) ? '<span style="color: green;">✓ Complete</span>' : '<span style="color: red;">✗ Empty</span>'; ?></td>
                                    <td><?php echo count($education); ?></td>
                                    <td><a href="cv_form.php#education" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Edit</a></td>
                                </tr>
                                <tr>
                                    <td>Work Experience</td>
                                    <td><?php echo !empty($work_experience) ? '<span style="color: green;">✓ Complete</span>' : '<span style="color: red;">✗ Empty</span>'; ?></td>
                                    <td><?php echo count($work_experience); ?></td>
                                    <td><a href="cv_form.php#experience" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Edit</a></td>
                                </tr>
                                <tr>
                                    <td>Skills</td>
                                    <td><?php echo !empty($skills) ? '<span style="color: green;">✓ Complete</span>' : '<span style="color: red;">✗ Empty</span>'; ?></td>
                                    <td><?php echo count($skills); ?></td>
                                    <td><a href="cv_form.php#skills" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Edit</a></td>
                                </tr>
                                <tr>
                                    <td>Projects</td>
                                    <td><?php echo !empty($projects) ? '<span style="color: green;">✓ Complete</span>' : '<span style="color: red;">✗ Empty</span>'; ?></td>
                                    <td><?php echo count($projects); ?></td>
                                    <td><a href="cv_form.php#projects" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Edit</a></td>
                                </tr>
                                <tr>
                                    <td>Certifications</td>
                                    <td><?php echo !empty($certifications) ? '<span style="color: green;">✓ Complete</span>' : '<span style="color: red;">✗ Empty</span>'; ?></td>
                                    <td><?php echo count($certifications); ?></td>
                                    <td><a href="cv_form.php#certifications" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Edit</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
