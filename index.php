<?php
require_once 'includes/functions.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Builder - Create Professional CVs Online</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder</div>
            <nav class="nav">
                <ul>
                    <?php if (isLoggedIn()): ?>
                        <li><a href="dashboard.php">Dashboard</a></li>
                        <?php if (isAdmin()): ?>
                            <li><a href="admin/dashboard.php">Admin Panel</a></li>
                        <?php endif; ?>
                        <li><a href="auth/logout.php">Logout (<?php echo $_SESSION['user_name']; ?>)</a></li>
                    <?php else: ?>
                        <li><a href="auth/login.php">Login</a></li>
                        <li><a href="auth/register.php">Register</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <div class="card" style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h1 style="font-size: 3rem; margin-bottom: 1rem;">Professional CV Builder</h1>
                <p style="font-size: 1.2rem; margin-bottom: 2rem;">Create stunning, professional CVs in minutes with our easy-to-use online CV builder.</p>

                <?php if (!isLoggedIn()): ?>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="auth/register.php" class="btn btn-success" style="font-size: 1.1rem; padding: 1rem 2rem;">Get Started Free</a>
                        <a href="auth/login.php" class="btn btn-secondary" style="font-size: 1.1rem; padding: 1rem 2rem;">Login</a>
                    </div>
                <?php else: ?>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="dashboard.php" class="btn btn-success" style="font-size: 1.1rem; padding: 1rem 2rem;">Go to Dashboard</a>
                        <a href="cv_form.php" class="btn btn-secondary" style="font-size: 1.1rem; padding: 1rem 2rem;">Create/Edit CV</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Features Section -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div class="card">
                    <h3 class="card-title">Easy to Use</h3>
                    <p>Simple and intuitive interface that guides you through the CV creation process step by step.</p>
                </div>

                <div class="card">
                    <h3 class="card-title">Professional Templates</h3>
                    <p>Choose from professionally designed templates that make your CV stand out to employers.</p>
                </div>

                <div class="card">
                    <h3 class="card-title">PDF Download</h3>
                    <p>Download your completed CV as a high-quality PDF file ready for printing or email submission.</p>
                </div>

                <div class="card">
                    <h3 class="card-title">Secure & Private</h3>
                    <p>Your personal information is stored securely and remains private. You control your data.</p>
                </div>

                <div class="card">
                    <h3 class="card-title">Edit Anytime</h3>
                    <p>Update your CV information anytime and regenerate your CV with the latest details.</p>
                </div>

                <div class="card">
                    <h3 class="card-title">Multiple Sections</h3>
                    <p>Add education, work experience, skills, projects, and certifications to showcase your expertise.</p>
                </div>
            </div>

            <!-- How it Works -->
            <div class="card" style="margin-top: 3rem;">
                <div class="card-header">
                    <h2 class="card-title">How It Works</h2>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div style="text-align: center;">
                        <div style="background: #667eea; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: bold; margin: 0 auto 1rem;">1</div>
                        <h4>Sign Up</h4>
                        <p>Create your free account to get started with CV Builder.</p>
                    </div>

                    <div style="text-align: center;">
                        <div style="background: #667eea; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: bold; margin: 0 auto 1rem;">2</div>
                        <h4>Fill Information</h4>
                        <p>Enter your personal details, education, experience, and skills.</p>
                    </div>

                    <div style="text-align: center;">
                        <div style="background: #667eea; color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: bold; margin: 0 auto 1rem;">3</div>
                        <h4>Preview & Download</h4>
                        <p>Preview your CV and download it as a professional PDF.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved. |  Hunain & Owais </p>
        </div>
    </footer>
</body>
</html>
