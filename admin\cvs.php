<?php
require_once '../includes/functions.php';
requireLogin();
requireAdmin();

// Get all CVs with user information
$stmt = $pdo->query("
    SELECT cv.*, u.full_name as user_name, u.email as user_email,
           (SELECT COUNT(*) FROM education WHERE cv_id = cv.id) as education_count,
           (SELECT COUNT(*) FROM work_experience WHERE cv_id = cv.id) as experience_count,
           (SELECT COUNT(*) FROM skills WHERE cv_id = cv.id) as skills_count,
           (SELECT COUNT(*) FROM projects WHERE cv_id = cv.id) as projects_count,
           (SELECT COUNT(*) FROM certifications WHERE cv_id = cv.id) as certifications_count
    FROM cv_data cv 
    JOIN users u ON cv.user_id = u.id 
    ORDER BY cv.created_at DESC
");
$cvs = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All CVs - CV Builder Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="dashboard.php">Admin Dashboard</a></li>
                    <li><a href="users.php">Manage Users</a></li>
                    <li><a href="cvs.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">All CVs</h1>
                    <p>View and manage all created CVs</p>
                </div>

                <?php displayMessages(); ?>

                <!-- CV Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($cvs); ?></div>
                        <div class="stat-label">Total CVs</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return strtotime($cv['created_at']) > strtotime('-30 days'); })); ?></div>
                        <div class="stat-label">Created This Month</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return !empty($cv['user_image']); })); ?></div>
                        <div class="stat-label">With Photos</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($cvs, function($cv) { return $cv['experience_count'] > 0; })); ?></div>
                        <div class="stat-label">With Experience</div>
                    </div>
                </div>

                <!-- CVs Table -->
                <?php if (!empty($cvs)): ?>
                    <div style="overflow-x: auto;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>CV ID</th>
                                    <th>CV Name</th>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Sections</th>
                                    <th>Photo</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cvs as $cv): ?>
                                    <tr>
                                        <td><?php echo $cv['id']; ?></td>
                                        <td><?php echo htmlspecialchars($cv['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($cv['user_name']); ?></td>
                                        <td><?php echo htmlspecialchars($cv['email']); ?></td>
                                        <td>
                                            <div style="font-size: 0.8rem;">
                                                <?php
                                                $sections = [];
                                                if ($cv['education_count'] > 0) $sections[] = "Edu({$cv['education_count']})";
                                                if ($cv['experience_count'] > 0) $sections[] = "Exp({$cv['experience_count']})";
                                                if ($cv['skills_count'] > 0) $sections[] = "Skills({$cv['skills_count']})";
                                                if ($cv['projects_count'] > 0) $sections[] = "Proj({$cv['projects_count']})";
                                                if ($cv['certifications_count'] > 0) $sections[] = "Cert({$cv['certifications_count']})";
                                                echo implode(', ', $sections) ?: 'None';
                                                ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($cv['user_image']): ?>
                                                <span style="color: green;">✓</span>
                                            <?php else: ?>
                                                <span style="color: #999;">✗</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M d, Y', strtotime($cv['created_at'])); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($cv['updated_at'])); ?></td>
                                        <td>
                                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                                <a href="view_cv.php?id=<?php echo $cv['id']; ?>" 
                                                   class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                                   View CV
                                                </a>
                                                <a href="view_user.php?id=<?php echo $cv['user_id']; ?>" 
                                                   class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                                   View User
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div style="text-align: center; padding: 2rem;">
                        <h3>No CVs Found</h3>
                        <p>No CVs have been created yet.</p>
                    </div>
                <?php endif; ?>

                <!-- Back to Dashboard -->
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
