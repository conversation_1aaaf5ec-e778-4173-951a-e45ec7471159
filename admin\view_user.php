<?php
require_once '../includes/functions.php';
requireLogin();
requireAdmin();

// Get user ID from URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setErrorMessage('Invalid user ID.');
    header('Location: users.php');
    exit();
}

$user_id = $_GET['id'];

// Get user data
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'user'");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    setErrorMessage('User not found.');
    header('Location: users.php');
    exit();
}

// Get user's CV data if exists
$cv_data = getUserCVData($user_id);
$has_cv = !empty($cv_data);

if ($has_cv) {
    $education = getUserEducation($cv_data['id']);
    $work_experience = getUserWorkExperience($cv_data['id']);
    $skills = getUserSkills($cv_data['id']);
    $projects = getUserProjects($cv_data['id']);
    $certifications = getUserCertifications($cv_data['id']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User - <?php echo htmlspecialchars($user['full_name']); ?> - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="dashboard.php">Admin Dashboard</a></li>
                    <li><a href="users.php">Manage Users</a></li>
                    <li><a href="cvs.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- User Information -->
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">User Details</h1>
                    <p>Complete information for <?php echo htmlspecialchars($user['full_name']); ?></p>
                </div>

                <?php displayMessages(); ?>

                <!-- User Basic Info -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h3>Personal Information</h3>
                        <p><strong>User ID:</strong> <?php echo $user['id']; ?></p>
                        <p><strong>Full Name:</strong> <?php echo htmlspecialchars($user['full_name']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                        <p><strong>User Type:</strong> <?php echo ucfirst($user['user_type']); ?></p>
                    </div>
                    
                    <div>
                        <h3>Account Activity</h3>
                        <p><strong>Registered:</strong> <?php echo date('M d, Y H:i', strtotime($user['created_at'])); ?></p>
                        <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($user['updated_at'])); ?></p>
                        <p><strong>CV Status:</strong> 
                            <?php if ($has_cv): ?>
                                <span style="color: var(--success-color); font-weight: bold;">✓ CV Created</span>
                            <?php else: ?>
                                <span style="color: var(--text-muted);">✗ No CV</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 2rem;">
                    <a href="users.php" class="btn btn-secondary">Back to Users</a>
                    <?php if ($has_cv): ?>
                        <a href="view_cv.php?id=<?php echo $cv_data['id']; ?>" class="btn btn-primary">View User's CV</a>
                        <a href="../cv_preview.php?user_id=<?php echo $user_id; ?>" class="btn btn-success" target="_blank">Preview CV</a>
                    <?php endif; ?>
                </div>

                <?php if ($has_cv): ?>
                <!-- CV Summary -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">CV Summary</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                        <!-- CV Basic Info -->
                        <div>
                            <h4>CV Information</h4>
                            <p><strong>CV Name:</strong> <?php echo htmlspecialchars($cv_data['full_name']); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($cv_data['email']); ?></p>
                            <p><strong>Contact:</strong> <?php echo htmlspecialchars($cv_data['contact']); ?></p>
                            <p><strong>Profile Photo:</strong> 
                                <?php if ($cv_data['user_image']): ?>
                                    <span style="color: var(--success-color);">✓ Uploaded</span>
                                <?php else: ?>
                                    <span style="color: var(--text-muted);">✗ No photo</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <!-- CV Statistics -->
                        <div>
                            <h4>CV Sections</h4>
                            <p><strong>Education:</strong> <?php echo count($education); ?> entries</p>
                            <p><strong>Work Experience:</strong> <?php echo count($work_experience); ?> entries</p>
                            <p><strong>Skills:</strong> <?php echo count($skills); ?> entries</p>
                            <p><strong>Projects:</strong> <?php echo count($projects); ?> entries</p>
                            <p><strong>Certifications:</strong> <?php echo count($certifications); ?> entries</p>
                        </div>
                        
                        <!-- CV Dates -->
                        <div>
                            <h4>CV Timeline</h4>
                            <p><strong>CV Created:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['created_at'])); ?></p>
                            <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['updated_at'])); ?></p>
                            <p><strong>Completeness:</strong> 
                                <?php 
                                $sections = 0;
                                if (!empty($education)) $sections++;
                                if (!empty($work_experience)) $sections++;
                                if (!empty($skills)) $sections++;
                                if (!empty($projects)) $sections++;
                                if (!empty($certifications)) $sections++;
                                $percentage = ($sections / 5) * 100;
                                echo $percentage . '% (' . $sections . '/5 sections)';
                                ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Detailed CV Sections -->
                <?php if (!empty($education)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Education (<?php echo count($education); ?> entries)</h3>
                    </div>
                    <div class="table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Degree</th>
                                    <th>Institute</th>
                                    <th>Year</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($education as $edu): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($edu['degree']); ?></td>
                                        <td><?php echo htmlspecialchars($edu['institute']); ?></td>
                                        <td><?php echo htmlspecialchars($edu['year']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($work_experience)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Work Experience (<?php echo count($work_experience); ?> entries)</h3>
                    </div>
                    <div class="table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Job Title</th>
                                    <th>Company</th>
                                    <th>Duration</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($work_experience as $exp): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($exp['job_title']); ?></td>
                                        <td><?php echo htmlspecialchars($exp['company']); ?></td>
                                        <td>
                                            <?php echo formatDate($exp['start_date']); ?> - 
                                            <?php echo $exp['end_date'] ? formatDate($exp['end_date']) : 'Present'; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars(substr($exp['description'], 0, 100)) . (strlen($exp['description']) > 100 ? '...' : ''); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($skills)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Skills (<?php echo count($skills); ?> entries)</h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <?php foreach ($skills as $skill): ?>
                            <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 0.5rem; border: 1px solid var(--border-color);">
                                <div style="font-weight: 600; margin-bottom: 0.5rem;"><?php echo htmlspecialchars($skill['skill_name']); ?></div>
                                <div style="color: var(--primary-color); font-size: 0.9rem;"><?php echo htmlspecialchars($skill['skill_level']); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php else: ?>
                <!-- No CV Message -->
                <div class="card">
                    <div style="text-align: center; padding: 2rem;">
                        <h3>No CV Created</h3>
                        <p>This user has not created a CV yet.</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
