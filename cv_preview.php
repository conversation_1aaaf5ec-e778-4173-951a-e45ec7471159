<?php
require_once 'includes/functions.php';
requireLogin();

// Get user's CV data
$cv_data = getUserCVData($_SESSION['user_id']);

if (!$cv_data) {
    setErrorMessage('No CV found. Please create your CV first.');
    header('Location: cv_form.php');
    exit();
}

$education = getUserEducation($cv_data['id']);
$work_experience = getUserWorkExperience($cv_data['id']);
$skills = getUserSkills($cv_data['id']);
$projects = getUserProjects($cv_data['id']);
$certifications = getUserCertifications($cv_data['id']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Preview - CV Builder</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .cv-container {
            display: flex;
            min-height: 100vh;
            max-width: 210mm;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            background: white;
        }

        .sidebar {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a5f 100%);
            color: white;
            width: 35%;
            padding: 40px 30px;
        }

        .main-content-cv {
            flex: 1;
            padding: 40px 30px;
            background: white;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid rgba(255,255,255,0.2);
            margin: 0 auto 30px;
            display: block;
        }

        .sidebar h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 300;
        }

        .sidebar .title {
            text-align: center;
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            font-weight: 300;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-section h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            font-weight: 600;
        }

        .contact-item {
            margin-bottom: 12px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .skill-item {
            margin-bottom: 15px;
        }

        .skill-name {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .skill-bar {
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: #4CAF50;
            border-radius: 3px;
        }

        .main-content-cv h2 {
            color: #2c5aa0;
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2c5aa0;
            font-weight: 600;
        }

        .experience-item, .education-item, .project-item, .cert-item {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .experience-item:last-child, .education-item:last-child,
        .project-item:last-child, .cert-item:last-child {
            border-bottom: none;
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .item-company {
            font-size: 1rem;
            color: #2c5aa0;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .item-date {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }

        .item-description {
            font-size: 0.9rem;
            color: #555;
            line-height: 1.6;
        }

        @media print {
            .no-print { display: none !important; }
            .cv-container { box-shadow: none; margin: 0; }
            body { background: white; }
        }
    </style>
</head>
<body>
    <header class="header no-print">
        <div class="container">
            <div class="logo">CV Builder</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="cv_form.php">Edit CV</a></li>
                    <li><a href="cv_preview.php">Preview CV</a></li>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Action Buttons -->
            <div class="card no-print" style="text-align: center; margin-bottom: 2rem;">
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="cv_form.php" class="btn btn-secondary">Edit CV</a>
                    <a href="download_cv.php" class="btn btn-success">Download PDF</a>
                    <button onclick="window.print()" class="btn btn-primary">Print CV</button>
                </div>
            </div>

            <!-- CV Preview with Modern Design -->
            <div class="cv-container">
                <!-- Sidebar -->
                <div class="sidebar">
                    <?php if ($cv_data['user_image']): ?>
                        <img src="uploads/<?php echo htmlspecialchars($cv_data['user_image']); ?>"
                             alt="Profile Photo" class="profile-photo">
                    <?php endif; ?>
                    <h1><?php echo htmlspecialchars($cv_data['full_name']); ?></h1>
                    <div class="title">Professional</div>

                    <div class="sidebar-section">
                        <h3>CONTACT</h3>
                        <div class="contact-item">
                            <span>📧</span> <?php echo htmlspecialchars($cv_data['email']); ?>
                        </div>
                        <div class="contact-item">
                            <span>📱</span> <?php echo htmlspecialchars($cv_data['contact']); ?>
                        </div>
                    </div>

                    <!-- Skills Section in Sidebar -->
                    <?php if (!empty($skills)): ?>
                    <div class="sidebar-section">
                        <h3>SKILLS</h3>
                        <?php foreach ($skills as $skill): ?>
                            <?php
                            $skill_percentage = 50; // default
                            switch($skill['skill_level']) {
                                case 'Beginner': $skill_percentage = 25; break;
                                case 'Intermediate': $skill_percentage = 50; break;
                                case 'Advanced': $skill_percentage = 75; break;
                                case 'Expert': $skill_percentage = 90; break;
                            }
                            ?>
                            <div class="skill-item">
                                <div class="skill-name"><?php echo htmlspecialchars($skill['skill_name']); ?></div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: <?php echo $skill_percentage; ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Main Content -->
                <div class="main-content-cv">

                    <!-- Work Experience Section -->
                    <?php if (!empty($work_experience)): ?>
                    <h2>PROFESSIONAL EXPERIENCE</h2>
                    <?php foreach ($work_experience as $exp): ?>
                        <div class="experience-item">
                            <div class="item-title"><?php echo htmlspecialchars($exp['job_title']); ?></div>
                            <div class="item-company"><?php echo htmlspecialchars($exp['company']); ?></div>
                            <div class="item-date">
                                <?php echo formatDate($exp['start_date']); ?> -
                                <?php echo $exp['end_date'] ? formatDate($exp['end_date']) : 'Present'; ?>
                            </div>
                            <?php if ($exp['description']): ?>
                                <div class="item-description">
                                    <?php echo nl2br(htmlspecialchars($exp['description'])); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Education Section -->
                    <?php if (!empty($education)): ?>
                    <h2>EDUCATION</h2>
                    <?php foreach ($education as $edu): ?>
                        <div class="education-item">
                            <div class="item-title"><?php echo htmlspecialchars($edu['degree']); ?></div>
                            <div class="item-company"><?php echo htmlspecialchars($edu['institute']); ?></div>
                            <div class="item-date"><?php echo htmlspecialchars($edu['year']); ?></div>
                        </div>
                    <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Projects Section -->
                    <?php if (!empty($projects)): ?>
                    <h2>PROJECTS</h2>
                    <?php foreach ($projects as $project): ?>
                        <div class="project-item">
                            <div class="item-title">
                                <?php echo htmlspecialchars($project['project_name']); ?>
                                <?php if ($project['github_link']): ?>
                                    <a href="<?php echo htmlspecialchars($project['github_link']); ?>"
                                       target="_blank" style="color: #2c5aa0; text-decoration: none; font-size: 0.9rem;">
                                        [GitHub]
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="item-description">
                                <?php echo nl2br(htmlspecialchars($project['description'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Certifications Section -->
                    <?php if (!empty($certifications)): ?>
                    <h2>CERTIFICATIONS</h2>
                    <?php foreach ($certifications as $cert): ?>
                        <div class="cert-item">
                            <div class="item-title"><?php echo htmlspecialchars($cert['certification_name']); ?></div>
                            <?php if ($cert['issuing_organization']): ?>
                                <div class="item-company"><?php echo htmlspecialchars($cert['issuing_organization']); ?></div>
                            <?php endif; ?>
                            <?php if ($cert['issue_date'] || $cert['expiry_date']): ?>
                                <div class="item-date">
                                    <?php if ($cert['issue_date']): ?>
                                        Issued: <?php echo formatDate($cert['issue_date']); ?>
                                    <?php endif; ?>
                                    <?php if ($cert['expiry_date']): ?>
                                        <?php echo ($cert['issue_date'] ? ' | ' : ''); ?>Expires: <?php echo formatDate($cert['expiry_date']); ?>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div> <!-- Close main-content-cv -->
            </div> <!-- Close cv-container -->
        </div>
    </main>

    <footer class="footer no-print">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
