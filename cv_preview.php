<?php
require_once 'includes/functions.php';
requireLogin();

// Get user's CV data
$cv_data = getUserCVData($_SESSION['user_id']);

if (!$cv_data) {
    setErrorMessage('No CV found. Please create your CV first.');
    header('Location: cv_form.php');
    exit();
}

$education = getUserEducation($cv_data['id']);
$work_experience = getUserWorkExperience($cv_data['id']);
$skills = getUserSkills($cv_data['id']);
$projects = getUserProjects($cv_data['id']);
$certifications = getUserCertifications($cv_data['id']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Preview - CV Builder</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            .cv-preview { box-shadow: none; margin: 0; }
            body { background: white; }
        }
    </style>
</head>
<body>
    <header class="header no-print">
        <div class="container">
            <div class="logo">CV Builder</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="cv_form.php">Edit CV</a></li>
                    <li><a href="cv_preview.php">Preview CV</a></li>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Action Buttons -->
            <div class="card no-print" style="text-align: center; margin-bottom: 2rem;">
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="cv_form.php" class="btn btn-secondary">Edit CV</a>
                    <a href="download_cv.php" class="btn btn-success">Download PDF</a>
                    <button onclick="window.print()" class="btn btn-primary">Print CV</button>
                </div>
            </div>

            <!-- CV Preview -->
            <div class="cv-preview">
                <!-- Header Section -->
                <div class="cv-header">
                    <?php if ($cv_data['user_image']): ?>
                        <img src="uploads/<?php echo htmlspecialchars($cv_data['user_image']); ?>" 
                             alt="Profile Photo" class="cv-photo">
                    <?php endif; ?>
                    <h1><?php echo htmlspecialchars($cv_data['full_name']); ?></h1>
                    <p style="font-size: 1.1rem; color: #666;">
                        <?php echo htmlspecialchars($cv_data['email']); ?> | 
                        <?php echo htmlspecialchars($cv_data['contact']); ?>
                    </p>
                </div>

                <!-- Education Section -->
                <?php if (!empty($education)): ?>
                <div class="cv-section">
                    <h3>Education</h3>
                    <?php foreach ($education as $edu): ?>
                        <div style="margin-bottom: 1rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($edu['degree']); ?>
                            </h4>
                            <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                <?php echo htmlspecialchars($edu['institute']); ?>
                            </p>
                            <p style="color: #666; font-size: 0.9rem;">
                                <?php echo htmlspecialchars($edu['year']); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Work Experience Section -->
                <?php if (!empty($work_experience)): ?>
                <div class="cv-section">
                    <h3>Work Experience</h3>
                    <?php foreach ($work_experience as $exp): ?>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($exp['job_title']); ?>
                            </h4>
                            <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                <?php echo htmlspecialchars($exp['company']); ?>
                            </p>
                            <p style="color: #666; font-size: 0.9rem; margin-bottom: 0.5rem;">
                                <?php echo formatDate($exp['start_date']); ?> - 
                                <?php echo $exp['end_date'] ? formatDate($exp['end_date']) : 'Present'; ?>
                            </p>
                            <?php if ($exp['description']): ?>
                                <p style="color: #555; line-height: 1.6;">
                                    <?php echo nl2br(htmlspecialchars($exp['description'])); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Skills Section -->
                <?php if (!empty($skills)): ?>
                <div class="cv-section">
                    <h3>Skills & Technologies</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <?php foreach ($skills as $skill): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f8f9fa; border-radius: 5px;">
                                <span style="font-weight: 600;"><?php echo htmlspecialchars($skill['skill_name']); ?></span>
                                <span style="color: #667eea; font-size: 0.9rem;"><?php echo htmlspecialchars($skill['skill_level']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Projects Section -->
                <?php if (!empty($projects)): ?>
                <div class="cv-section">
                    <h3>Projects</h3>
                    <?php foreach ($projects as $project): ?>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($project['project_name']); ?>
                                <?php if ($project['github_link']): ?>
                                    <a href="<?php echo htmlspecialchars($project['github_link']); ?>" 
                                       target="_blank" style="font-size: 0.8rem; color: #667eea; text-decoration: none;">
                                        [GitHub]
                                    </a>
                                <?php endif; ?>
                            </h4>
                            <p style="color: #555; line-height: 1.6;">
                                <?php echo nl2br(htmlspecialchars($project['description'])); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Certifications Section -->
                <?php if (!empty($certifications)): ?>
                <div class="cv-section">
                    <h3>Certifications</h3>
                    <?php foreach ($certifications as $cert): ?>
                        <div style="margin-bottom: 1rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($cert['certification_name']); ?>
                            </h4>
                            <?php if ($cert['issuing_organization']): ?>
                                <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                    <?php echo htmlspecialchars($cert['issuing_organization']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($cert['issue_date'] || $cert['expiry_date']): ?>
                                <p style="color: #666; font-size: 0.9rem;">
                                    <?php if ($cert['issue_date']): ?>
                                        Issued: <?php echo formatDate($cert['issue_date']); ?>
                                    <?php endif; ?>
                                    <?php if ($cert['expiry_date']): ?>
                                        | Expires: <?php echo formatDate($cert['expiry_date']); ?>
                                    <?php endif; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="footer no-print">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
