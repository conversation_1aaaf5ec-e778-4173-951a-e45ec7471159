# CV Builder - Professional CV Creation Platform

A comprehensive web-based CV Builder application built with PHP and MySQL that allows users to create, edit, and download professional CVs.

## Features

### User Features
- **User Registration & Login**: Secure authentication system
- **CV Creation**: Comprehensive form to collect all CV information
- **Multiple Sections**: 
  - Personal Information with photo upload
  - Education history
  - Work experience
  - Skills & technologies with proficiency levels
  - Projects with GitHub links
  - Certifications with dates
- **CV Preview**: Real-time preview of the formatted CV
- **PDF Download**: Download CV as HTML file (can be converted to PDF)
- **Edit & Update**: Modify CV information anytime
- **Dashboard**: Track CV completion status and statistics

### Admin Features
- **Admin Dashboard**: Overview of platform statistics
- **User Management**: View all registered users
- **CV Management**: View all created CVs
- **User Analytics**: Track user activity and CV creation

## Technical Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache/Nginx
- **Extensions**: PDO MySQL, GD (for image handling)

## Installation

### Method 1: Using Setup Script (Recommended)

1. **Download/Clone** the project files to your web server directory
2. **Navigate** to `http://yourserver/cv-builder/setup.php`
3. **Follow the setup wizard**:
   - Configure database connection
   - Create database tables
   - Complete installation
4. **Delete** `setup.php` file after installation for security

### Method 2: Manual Installation

1. **Create Database**:
   ```sql
   CREATE DATABASE cv_builder;
   ```

2. **Import Schema**:
   ```bash
   mysql -u username -p cv_builder < database/schema.sql
   ```

3. **Configure Database**:
   - Edit `config/database.php` with your database credentials

4. **Set Permissions**:
   ```bash
   chmod 755 uploads/
   ```

## Default Admin Account

- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change the admin password immediately after first login!

## File Structure

```
cv-builder/
├── admin/                  # Admin panel files
│   ├── dashboard.php      # Admin dashboard
│   ├── users.php          # User management
│   ├── cvs.php           # CV management
│   └── view_cv.php       # CV viewer for admin
├── assets/                # Static assets
│   ├── css/
│   │   └── style.css     # Main stylesheet
│   └── js/
│       └── script.js     # JavaScript functionality
├── auth/                  # Authentication files
│   ├── login.php         # Login page
│   ├── register.php      # Registration page
│   └── logout.php        # Logout handler
├── config/                # Configuration files
│   └── database.php      # Database connection
├── database/              # Database files
│   └── schema.sql        # Database schema
├── includes/              # Helper files
│   └── functions.php     # Common functions
├── uploads/               # User uploaded files
│   └── .htaccess         # Security rules
├── index.php             # Homepage
├── dashboard.php         # User dashboard
├── cv_form.php          # CV creation/editing form
├── cv_preview.php       # CV preview page
├── process_cv.php       # CV form processor
├── download_cv.php      # CV download handler
├── setup.php            # Installation script
└── README.md            # This file
```

## Usage Guide

### For Users

1. **Register**: Create a new account
2. **Login**: Access your dashboard
3. **Create CV**: Fill in your information using the comprehensive form
4. **Preview**: Review your CV formatting
5. **Download**: Get your CV as an HTML file
6. **Edit**: Update your information anytime

### For Administrators

1. **Login**: Use admin credentials
2. **Dashboard**: View platform statistics
3. **Manage Users**: View and manage registered users
4. **View CVs**: Browse all created CVs
5. **Analytics**: Track platform usage

## Security Features

- **Password Hashing**: Secure password storage using PHP's password_hash()
- **SQL Injection Protection**: Prepared statements throughout
- **XSS Prevention**: Input sanitization and output escaping
- **File Upload Security**: Restricted file types and secure upload handling
- **Session Management**: Secure session handling
- **Admin Protection**: Role-based access control

## Customization

### Adding New CV Sections

1. **Database**: Add new table in `database/schema.sql`
2. **Form**: Add form fields in `cv_form.php`
3. **Processing**: Handle data in `process_cv.php`
4. **Display**: Add section in `cv_preview.php`
5. **Functions**: Add helper functions in `includes/functions.php`

### Styling

- Modify `assets/css/style.css` for visual customization
- The design is responsive and mobile-friendly
- Uses CSS Grid and Flexbox for layouts

### PDF Generation

The current implementation downloads HTML files. To add true PDF generation:

1. **Install TCPDF or similar library**:
   ```bash
   composer require tecnickcom/tcpdf
   ```

2. **Modify `download_cv.php`** to use the PDF library

## Database Schema

### Tables

- **users**: User accounts and authentication
- **cv_data**: Main CV information
- **education**: Education entries
- **work_experience**: Work history
- **skills**: Skills and proficiency levels
- **projects**: Project portfolio
- **certifications**: Professional certifications

### Relationships

- All CV-related tables are linked to `cv_data` via foreign keys
- Cascade delete ensures data integrity
- Normalized structure for efficient storage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check the documentation
2. Review the code comments
3. Test with the provided demo data
4. Ensure all requirements are met

## Future Enhancements

- **PDF Generation**: True PDF export functionality
- **Templates**: Multiple CV templates
- **Email Integration**: Email CV directly to employers
- **Social Login**: OAuth integration
- **Multi-language**: Internationalization support
- **Analytics**: Advanced user analytics
- **API**: RESTful API for mobile apps

## Demo Data

The system includes a default admin account for testing. You can create test users and CVs to explore all features.

---

**Built with ❤️ using PHP and MySQL**
