<?php
require_once '../includes/functions.php';
requireLogin();
requireAdmin();

// Get statistics
$stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE user_type = 'user'");
$total_users = $stmt->fetch()['total_users'];

$stmt = $pdo->query("SELECT COUNT(*) as total_cvs FROM cv_data");
$total_cvs = $stmt->fetch()['total_cvs'];

$stmt = $pdo->query("SELECT COUNT(*) as total_education FROM education");
$total_education = $stmt->fetch()['total_education'];

$stmt = $pdo->query("SELECT COUNT(*) as total_experience FROM work_experience");
$total_experience = $stmt->fetch()['total_experience'];

// Get recent users
$stmt = $pdo->query("SELECT id, full_name, email, created_at FROM users WHERE user_type = 'user' OR<PERSON><PERSON> BY created_at DESC LIMIT 10");
$recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent CVs
$stmt = $pdo->query("
    SELECT cv.id, cv.full_name, cv.email, cv.created_at, u.full_name as user_name 
    FROM cv_data cv 
    JOIN users u ON cv.user_id = u.id 
    ORDER BY cv.created_at DESC 
    LIMIT 10
");
$recent_cvs = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - CV Builder</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="dashboard.php">Admin Dashboard</a></li>
                    <li><a href="users.php">Manage Users</a></li>
                    <li><a href="cvs.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Admin Dashboard</h1>
                    <p>Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?>! Manage your CV Builder platform.</p>
                </div>

                <?php displayMessages(); ?>

                <!-- Statistics -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_users; ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_cvs; ?></div>
                        <div class="stat-label">CVs Created</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_education; ?></div>
                        <div class="stat-label">Education Entries</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_experience; ?></div>
                        <div class="stat-label">Work Experiences</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <a href="users.php" class="btn btn-primary">Manage Users</a>
                        <a href="cvs.php" class="btn btn-secondary">View All CVs</a>
                        <a href="../dashboard.php" class="btn btn-success">User Dashboard</a>
                    </div>
                </div>

                <!-- Recent Users -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Users</h3>
                    </div>
                    
                    <?php if (!empty($recent_users)): ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <a href="view_user.php?id=<?php echo $user['id']; ?>" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="users.php" class="btn btn-primary">View All Users</a>
                        </div>
                    <?php else: ?>
                        <p>No users registered yet.</p>
                    <?php endif; ?>
                </div>

                <!-- Recent CVs -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent CVs</h3>
                    </div>
                    
                    <?php if (!empty($recent_cvs)): ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>CV ID</th>
                                    <th>CV Name</th>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_cvs as $cv): ?>
                                    <tr>
                                        <td><?php echo $cv['id']; ?></td>
                                        <td><?php echo htmlspecialchars($cv['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($cv['user_name']); ?></td>
                                        <td><?php echo htmlspecialchars($cv['email']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($cv['created_at'])); ?></td>
                                        <td>
                                            <a href="view_cv.php?id=<?php echo $cv['id']; ?>" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="cvs.php" class="btn btn-primary">View All CVs</a>
                        </div>
                    <?php else: ?>
                        <p>No CVs created yet.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
