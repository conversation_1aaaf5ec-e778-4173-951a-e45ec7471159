<?php
require_once 'includes/functions.php';
requireLogin();

// Get user's CV data
$cv_data = getUserCVData($_SESSION['user_id']);

if (!$cv_data) {
    setErrorMessage('No CV found. Please create your CV first.');
    header('Location: cv_form.php');
    exit();
}

$education = getUserEducation($cv_data['id']);
$work_experience = getUserWorkExperience($cv_data['id']);
$skills = getUserSkills($cv_data['id']);
$projects = getUserProjects($cv_data['id']);
$certifications = getUserCertifications($cv_data['id']);

// Generate modern CV HTML with sidebar design
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CV - ' . htmlspecialchars($cv_data['full_name']) . '</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .cv-container {
            display: flex;
            min-height: 100vh;
            max-width: 210mm;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .sidebar {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a5f 100%);
            color: white;
            width: 35%;
            padding: 40px 30px;
            position: relative;
        }

        .main-content {
            flex: 1;
            padding: 40px 30px;
            background: white;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid rgba(255,255,255,0.2);
            margin: 0 auto 30px;
            display: block;
        }

        .sidebar h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 300;
        }

        .sidebar .title {
            text-align: center;
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            font-weight: 300;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-section h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            font-weight: 600;
        }

        .contact-item {
            margin-bottom: 12px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .contact-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            opacity: 0.8;
        }

        .skill-item {
            margin-bottom: 15px;
        }

        .skill-name {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .skill-bar {
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-progress {
            height: 100%;
            background: #4CAF50;
            border-radius: 3px;
        }

        .main-content h2 {
            color: #2c5aa0;
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2c5aa0;
            font-weight: 600;
        }

        .experience-item, .education-item, .project-item, .cert-item {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .experience-item:last-child, .education-item:last-child,
        .project-item:last-child, .cert-item:last-child {
            border-bottom: none;
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .item-company {
            font-size: 1rem;
            color: #2c5aa0;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .item-date {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }

        .item-description {
            font-size: 0.9rem;
            color: #555;
            line-height: 1.6;
        }

        @media print {
            body { margin: 0; }
            .cv-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <div class="sidebar">
            ' . ($cv_data['user_image'] ? '<img src="uploads/' . htmlspecialchars($cv_data['user_image']) . '" alt="Profile Photo" class="profile-photo">' : '') . '
            <h1>' . htmlspecialchars($cv_data['full_name']) . '</h1>
            <div class="title">Professional</div>

            <div class="sidebar-section">
                <h3>CONTACT</h3>
                <div class="contact-item">
                    <span>📧</span> ' . htmlspecialchars($cv_data['email']) . '
                </div>
                <div class="contact-item">
                    <span>📱</span> ' . htmlspecialchars($cv_data['contact']) . '
                </div>
            </div>';

// Skills Section in Sidebar
if (!empty($skills)) {
    $html .= '<div class="sidebar-section">
                <h3>SKILLS</h3>';
    foreach ($skills as $skill) {
        $skill_percentage = 50; // default
        switch($skill['skill_level']) {
            case 'Beginner': $skill_percentage = 25; break;
            case 'Intermediate': $skill_percentage = 50; break;
            case 'Advanced': $skill_percentage = 75; break;
            case 'Expert': $skill_percentage = 90; break;
        }
        $html .= '<div class="skill-item">
                    <div class="skill-name">' . htmlspecialchars($skill['skill_name']) . '</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: ' . $skill_percentage . '%"></div>
                    </div>
                  </div>';
    }
    $html .= '</div>';
}

$html .= '</div>'; // Close sidebar

// Main content area
$html .= '<div class="main-content">';

// Work Experience Section
if (!empty($work_experience)) {
    $html .= '<h2>PROFESSIONAL EXPERIENCE</h2>';
    foreach ($work_experience as $exp) {
        $html .= '<div class="experience-item">
            <div class="item-title">' . htmlspecialchars($exp['job_title']) . '</div>
            <div class="item-company">' . htmlspecialchars($exp['company']) . '</div>
            <div class="item-date">' . formatDate($exp['start_date']) . ' - ' .
                ($exp['end_date'] ? formatDate($exp['end_date']) : 'Present') . '</div>';
        if ($exp['description']) {
            $html .= '<div class="item-description">' . nl2br(htmlspecialchars($exp['description'])) . '</div>';
        }
        $html .= '</div>';
    }
}

// Education Section
if (!empty($education)) {
    $html .= '<h2>EDUCATION</h2>';
    foreach ($education as $edu) {
        $html .= '<div class="education-item">
            <div class="item-title">' . htmlspecialchars($edu['degree']) . '</div>
            <div class="item-company">' . htmlspecialchars($edu['institute']) . '</div>
            <div class="item-date">' . htmlspecialchars($edu['year']) . '</div>
        </div>';
    }
}

// Projects Section
if (!empty($projects)) {
    $html .= '<h2>PROJECTS</h2>';
    foreach ($projects as $project) {
        $html .= '<div class="project-item">
            <div class="item-title">' . htmlspecialchars($project['project_name']);
        if ($project['github_link']) {
            $html .= ' <a href="' . htmlspecialchars($project['github_link']) . '" style="color: #2c5aa0; text-decoration: none; font-size: 0.9rem;">[GitHub]</a>';
        }
        $html .= '</div>
            <div class="item-description">' . nl2br(htmlspecialchars($project['description'])) . '</div>
        </div>';
    }
}

// Certifications Section
if (!empty($certifications)) {
    $html .= '<h2>CERTIFICATIONS</h2>';
    foreach ($certifications as $cert) {
        $html .= '<div class="cert-item">
            <div class="item-title">' . htmlspecialchars($cert['certification_name']) . '</div>';
        if ($cert['issuing_organization']) {
            $html .= '<div class="item-company">' . htmlspecialchars($cert['issuing_organization']) . '</div>';
        }
        if ($cert['issue_date'] || $cert['expiry_date']) {
            $html .= '<div class="item-date">';
            if ($cert['issue_date']) {
                $html .= 'Issued: ' . formatDate($cert['issue_date']);
            }
            if ($cert['expiry_date']) {
                $html .= ($cert['issue_date'] ? ' | ' : '') . 'Expires: ' . formatDate($cert['expiry_date']);
            }
            $html .= '</div>';
        }
        $html .= '</div>';
    }
}

$html .= '</div>'; // Close main-content
$html .= '</div>'; // Close cv-container
$html .= '</body></html>';

// Set headers for HTML download (can be printed to PDF)
$filename = 'CV_' . preg_replace('/[^a-zA-Z0-9]/', '_', $cv_data['full_name']) . '_' . date('Y-m-d') . '.html';

header('Content-Type: text/html');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

echo $html;

// Note: For actual PDF generation, you would need to install a PDF library
// Here's an example of how you could integrate TCPDF:
/*
require_once('tcpdf/tcpdf.php');

$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor($cv_data['full_name']);
$pdf->SetTitle('CV - ' . $cv_data['full_name']);
$pdf->SetSubject('Curriculum Vitae');

$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

$pdf->AddPage();
$pdf->writeHTML($html, true, false, true, false, '');

$filename = 'CV_' . preg_replace('/[^a-zA-Z0-9]/', '_', $cv_data['full_name']) . '_' . date('Y-m-d') . '.pdf';
$pdf->Output($filename, 'D');
*/
?>
