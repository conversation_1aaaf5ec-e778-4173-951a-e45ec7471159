<?php
require_once 'includes/functions.php';
requireLogin();

// Get user's CV data
$cv_data = getUserCVData($_SESSION['user_id']);

if (!$cv_data) {
    setErrorMessage('No CV found. Please create your CV first.');
    header('Location: cv_form.php');
    exit();
}

$education = getUserEducation($cv_data['id']);
$work_experience = getUserWorkExperience($cv_data['id']);
$skills = getUserSkills($cv_data['id']);
$projects = getUserProjects($cv_data['id']);
$certifications = getUserCertifications($cv_data['id']);

// Generate HTML content for PDF
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CV - ' . htmlspecialchars($cv_data['full_name']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .cv-header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .cv-header h1 {
            margin: 0;
            color: #333;
            font-size: 2.5em;
        }
        .cv-header p {
            margin: 10px 0;
            font-size: 1.1em;
            color: #666;
        }
        .cv-section {
            margin-bottom: 30px;
        }
        .cv-section h3 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .entry {
            margin-bottom: 20px;
        }
        .entry h4 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.1em;
        }
        .entry .company {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .entry .date {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .entry .description {
            color: #555;
            line-height: 1.6;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .skill-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .skill-name {
            font-weight: bold;
        }
        .skill-level {
            color: #667eea;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="cv-header">
        <h1>' . htmlspecialchars($cv_data['full_name']) . '</h1>
        <p>' . htmlspecialchars($cv_data['email']) . ' | ' . htmlspecialchars($cv_data['contact']) . '</p>
    </div>';

// Education Section
if (!empty($education)) {
    $html .= '<div class="cv-section">
        <h3>Education</h3>';
    foreach ($education as $edu) {
        $html .= '<div class="entry">
            <h4>' . htmlspecialchars($edu['degree']) . '</h4>
            <div class="company">' . htmlspecialchars($edu['institute']) . '</div>
            <div class="date">' . htmlspecialchars($edu['year']) . '</div>
        </div>';
    }
    $html .= '</div>';
}

// Work Experience Section
if (!empty($work_experience)) {
    $html .= '<div class="cv-section">
        <h3>Work Experience</h3>';
    foreach ($work_experience as $exp) {
        $html .= '<div class="entry">
            <h4>' . htmlspecialchars($exp['job_title']) . '</h4>
            <div class="company">' . htmlspecialchars($exp['company']) . '</div>
            <div class="date">' . formatDate($exp['start_date']) . ' - ' . 
                ($exp['end_date'] ? formatDate($exp['end_date']) : 'Present') . '</div>';
        if ($exp['description']) {
            $html .= '<div class="description">' . nl2br(htmlspecialchars($exp['description'])) . '</div>';
        }
        $html .= '</div>';
    }
    $html .= '</div>';
}

// Skills Section
if (!empty($skills)) {
    $html .= '<div class="cv-section">
        <h3>Skills & Technologies</h3>
        <div class="skills-grid">';
    foreach ($skills as $skill) {
        $html .= '<div class="skill-item">
            <span class="skill-name">' . htmlspecialchars($skill['skill_name']) . '</span>
            <span class="skill-level">' . htmlspecialchars($skill['skill_level']) . '</span>
        </div>';
    }
    $html .= '</div></div>';
}

// Projects Section
if (!empty($projects)) {
    $html .= '<div class="cv-section">
        <h3>Projects</h3>';
    foreach ($projects as $project) {
        $html .= '<div class="entry">
            <h4>' . htmlspecialchars($project['project_name']);
        if ($project['github_link']) {
            $html .= ' <a href="' . htmlspecialchars($project['github_link']) . '">[GitHub]</a>';
        }
        $html .= '</h4>
            <div class="description">' . nl2br(htmlspecialchars($project['description'])) . '</div>
        </div>';
    }
    $html .= '</div>';
}

// Certifications Section
if (!empty($certifications)) {
    $html .= '<div class="cv-section">
        <h3>Certifications</h3>';
    foreach ($certifications as $cert) {
        $html .= '<div class="entry">
            <h4>' . htmlspecialchars($cert['certification_name']) . '</h4>';
        if ($cert['issuing_organization']) {
            $html .= '<div class="company">' . htmlspecialchars($cert['issuing_organization']) . '</div>';
        }
        if ($cert['issue_date'] || $cert['expiry_date']) {
            $html .= '<div class="date">';
            if ($cert['issue_date']) {
                $html .= 'Issued: ' . formatDate($cert['issue_date']);
            }
            if ($cert['expiry_date']) {
                $html .= ($cert['issue_date'] ? ' | ' : '') . 'Expires: ' . formatDate($cert['expiry_date']);
            }
            $html .= '</div>';
        }
        $html .= '</div>';
    }
    $html .= '</div>';
}

$html .= '</body></html>';

// For now, we'll create a simple HTML-to-PDF conversion
// In a production environment, you would use a library like TCPDF, FPDF, or wkhtmltopdf

// Set headers for PDF download
$filename = 'CV_' . preg_replace('/[^a-zA-Z0-9]/', '_', $cv_data['full_name']) . '_' . date('Y-m-d') . '.html';

header('Content-Type: text/html');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

echo $html;

// Note: For actual PDF generation, you would need to install a PDF library
// Here's an example of how you could integrate TCPDF:
/*
require_once('tcpdf/tcpdf.php');

$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor($cv_data['full_name']);
$pdf->SetTitle('CV - ' . $cv_data['full_name']);
$pdf->SetSubject('Curriculum Vitae');

$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

$pdf->AddPage();
$pdf->writeHTML($html, true, false, true, false, '');

$filename = 'CV_' . preg_replace('/[^a-zA-Z0-9]/', '_', $cv_data['full_name']) . '_' . date('Y-m-d') . '.pdf';
$pdf->Output($filename, 'D');
*/
?>
