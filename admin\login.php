<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in as admin
if (isLoggedIn() && isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error = 'Please enter both email and password.';
    } else {
        // Check admin credentials only
        $stmt = $pdo->prepare("SELECT id, full_name, email, password, user_type FROM users WHERE email = ? AND user_type = 'admin'");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['full_name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_type'] = $user['user_type'];
            
            header('Location: dashboard.php');
            exit();
        } else {
            $error = 'Invalid admin credentials.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - CV Builder</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            position: relative;
            overflow: hidden;
        }
        
        .admin-login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite;
        }
        
        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
        
        .admin-login-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 1.5rem;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: var(--shadow-xl);
            position: relative;
            z-index: 1;
            backdrop-filter: blur(20px);
            animation: cardSlideIn 0.8s ease-out;
        }
        
        @keyframes cardSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .admin-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .admin-logo h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: logoGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes logoGlow {
            from { filter: drop-shadow(0 0 10px rgba(239, 68, 68, 0.3)); }
            to { filter: drop-shadow(0 0 20px rgba(239, 68, 68, 0.6)); }
        }
        
        .admin-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .admin-description {
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .admin-form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .admin-form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .admin-form-control {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            font-size: 1rem;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        
        .admin-form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
            transform: translateY(-2px);
            background: var(--bg-tertiary);
        }
        
        .admin-btn {
            width: 100%;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1.1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }
        
        .admin-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .admin-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
            filter: brightness(1.1);
        }
        
        .admin-btn:hover::before {
            left: 100%;
        }
        
        .admin-btn:active {
            transform: translateY(-1px);
        }
        
        .admin-alert {
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1px solid;
            animation: alertSlideIn 0.5s ease-out;
        }
        
        .admin-alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border-color: rgba(239, 68, 68, 0.2);
        }
        
        .admin-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }
        
        .admin-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .admin-links a:hover {
            color: #ef4444;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
            padding: 0.75rem;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 0.5rem;
            font-size: 0.8rem;
            color: var(--text-muted);
        }
        
        .security-icon {
            width: 16px;
            height: 16px;
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-login-card">
            <div class="admin-logo">
                <h1>🛡️ ADMIN</h1>
                <div class="admin-subtitle">CV Builder Administration</div>
                <div class="admin-description">Secure access to admin panel</div>
            </div>
            
            <?php if ($error): ?>
                <div class="admin-alert admin-alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="admin-form-group">
                    <label for="email" class="admin-form-label">Admin Email</label>
                    <input type="email" id="email" name="email" class="admin-form-control" 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                           placeholder="Enter admin email" required>
                </div>
                
                <div class="admin-form-group">
                    <label for="password" class="admin-form-label">Admin Password</label>
                    <input type="password" id="password" name="password" class="admin-form-control" 
                           placeholder="Enter admin password" required>
                </div>
                
                <button type="submit" class="admin-btn">Access Admin Panel</button>
            </form>
            
            <div class="security-badge">
                <span class="security-icon">🔒</span>
                <span>Secure Admin Authentication</span>
            </div>
            
            <div class="admin-links">
                <a href="../index.php">← Back to Main Site</a> | 
                <a href="../auth/login.php">User Login</a>
            </div>
            
            <div style="margin-top: 2rem; padding: 1rem; background: var(--bg-secondary); border-radius: 0.5rem; font-size: 0.8rem; color: var(--text-muted);">
                <strong>Demo Admin Credentials:</strong><br>
                Email: <EMAIL><br>
                Password: admin123
            </div>
        </div>
    </div>
</body>
</html>
