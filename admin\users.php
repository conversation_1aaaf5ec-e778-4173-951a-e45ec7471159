<?php
require_once '../includes/functions.php';
requireLogin();
requireAdmin();

// Handle user deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $user_id = $_GET['delete'];
    
    // Don't allow deleting admin users
    $stmt = $pdo->prepare("SELECT user_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if ($user && $user['user_type'] !== 'admin') {
        try {
            $pdo->beginTransaction();
            
            // Delete user's CV data (cascade will handle related tables)
            $stmt = $pdo->prepare("DELETE FROM cv_data WHERE user_id = ?");
            $stmt->execute([$user_id]);
            
            // Delete user
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $pdo->commit();
            setSuccessMessage('User deleted successfully.');
        } catch (Exception $e) {
            $pdo->rollBack();
            setErrorMessage('Error deleting user: ' . $e->getMessage());
        }
    } else {
        setErrorMessage('Cannot delete admin users.');
    }
    
    header('Location: users.php');
    exit();
}

// Get all users
$stmt = $pdo->query("
    SELECT u.*, 
           (SELECT COUNT(*) FROM cv_data WHERE user_id = u.id) as cv_count
    FROM users u 
    WHERE u.user_type = 'user' 
    ORDER BY u.created_at DESC
");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - CV Builder Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="dashboard.php">Admin Dashboard</a></li>
                    <li><a href="users.php">Manage Users</a></li>
                    <li><a href="cvs.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Manage Users</h1>
                    <p>View and manage all registered users</p>
                </div>

                <?php displayMessages(); ?>

                <!-- Users Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($users); ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($users, function($u) { return $u['cv_count'] > 0; })); ?></div>
                        <div class="stat-label">Users with CVs</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($users, function($u) { return strtotime($u['created_at']) > strtotime('-30 days'); })); ?></div>
                        <div class="stat-label">New This Month</div>
                    </div>
                </div>

                <!-- Users Table -->
                <?php if (!empty($users)): ?>
                    <div style="overflow-x: auto;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>CVs</th>
                                    <th>Registered</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <?php if ($user['cv_count'] > 0): ?>
                                                <span style="color: green; font-weight: bold;"><?php echo $user['cv_count']; ?></span>
                                            <?php else: ?>
                                                <span style="color: #999;">0</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($user['updated_at'])); ?></td>
                                        <td>
                                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                                <a href="view_user.php?id=<?php echo $user['id']; ?>" 
                                                   class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                                   View
                                                </a>
                                                <?php if ($user['cv_count'] > 0): ?>
                                                    <a href="view_user_cv.php?user_id=<?php echo $user['id']; ?>" 
                                                       class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                                       View CV
                                                    </a>
                                                <?php endif; ?>
                                                <a href="users.php?delete=<?php echo $user['id']; ?>" 
                                                   class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;"
                                                   onclick="return confirm('Are you sure you want to delete this user? This will also delete their CV data.')">
                                                   Delete
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div style="text-align: center; padding: 2rem;">
                        <h3>No Users Found</h3>
                        <p>No users have registered yet.</p>
                    </div>
                <?php endif; ?>

                <!-- Back to Dashboard -->
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
