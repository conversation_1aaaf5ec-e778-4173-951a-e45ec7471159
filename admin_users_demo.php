<?php
// Demo Admin Users Management Page
session_start();

// Simulate admin login for demo
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin User';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_type'] = 'admin';

// Mock users data
$users = [
    ['id' => 1, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-15 10:30:00', 'updated_at' => '2024-01-15 11:00:00'],
    ['id' => 2, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-14 14:20:00', 'updated_at' => '2024-01-14 15:30:00'],
    ['id' => 3, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-13 09:15:00', 'updated_at' => '2024-01-13 10:45:00'],
    ['id' => 4, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-12 16:45:00', 'updated_at' => '2024-01-12 17:20:00'],
    ['id' => 5, 'full_name' => 'David Brown', 'email' => '<EMAIL>', 'cv_count' => 0, 'created_at' => '2024-01-11 11:30:00', 'updated_at' => '2024-01-11 11:30:00'],
    ['id' => 6, 'full_name' => 'Emily Davis', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-10 13:15:00', 'updated_at' => '2024-01-10 14:00:00'],
    ['id' => 7, 'full_name' => 'Robert Miller', 'email' => '<EMAIL>', 'cv_count' => 0, 'created_at' => '2024-01-09 08:45:00', 'updated_at' => '2024-01-09 08:45:00'],
    ['id' => 8, 'full_name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'cv_count' => 1, 'created_at' => '2024-01-08 15:20:00', 'updated_at' => '2024-01-08 16:10:00']
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - CV Builder Admin</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="admin_demo.php">Admin Dashboard</a></li>
                    <li><a href="admin_users_demo.php">Manage Users</a></li>
                    <li><a href="admin_cvs_demo.php">View All CVs</a></li>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Manage Users</h1>
                    <p>View and manage all registered users</p>
                </div>

                <!-- Users Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($users); ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($users, function($u) { return $u['cv_count'] > 0; })); ?></div>
                        <div class="stat-label">Users with CVs</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($users, function($u) { return strtotime($u['created_at']) > strtotime('-30 days'); })); ?></div>
                        <div class="stat-label">New This Month</div>
                    </div>
                </div>

                <!-- Users Table -->
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>CVs</th>
                                <th>Registered</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <?php if ($user['cv_count'] > 0): ?>
                                            <span style="color: green; font-weight: bold;"><?php echo $user['cv_count']; ?></span>
                                        <?php else: ?>
                                            <span style="color: #999;">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['updated_at'])); ?></td>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                            <a href="#" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                               View
                                            </a>
                                            <?php if ($user['cv_count'] > 0): ?>
                                                <a href="#" class="btn btn-primary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                                   View CV
                                                </a>
                                            <?php endif; ?>
                                            <a href="#" class="btn btn-danger" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;"
                                               onclick="return confirm('Are you sure you want to delete this user? This will also delete their CV data.')">
                                               Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Back to Dashboard -->
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="admin_demo.php" class="btn btn-primary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
