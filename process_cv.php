<?php
require_once 'includes/functions.php';
requireLogin();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: cv_form.php');
    exit();
}

try {
    $pdo->beginTransaction();
    
    // Get form data
    $full_name = sanitize($_POST['full_name']);
    $email = sanitize($_POST['email']);
    $contact = sanitize($_POST['contact']);
    
    // Handle image upload
    $user_image = '';
    if (isset($_FILES['user_image']) && $_FILES['user_image']['error'] === UPLOAD_ERR_OK) {
        $user_image = uploadImage($_FILES['user_image']);
        if (!$user_image) {
            throw new Exception('Failed to upload image.');
        }
    }
    
    // Check if CV already exists
    $existing_cv = getUserCVData($_SESSION['user_id']);
    
    if ($existing_cv) {
        // Update existing CV
        $cv_id = $existing_cv['id'];
        
        // Keep existing image if no new image uploaded
        if (empty($user_image)) {
            $user_image = $existing_cv['user_image'];
        }
        
        $stmt = $pdo->prepare("UPDATE cv_data SET full_name = ?, email = ?, contact = ?, user_image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$full_name, $email, $contact, $user_image, $cv_id]);
        
        // Delete existing related data
        $pdo->prepare("DELETE FROM education WHERE cv_id = ?")->execute([$cv_id]);
        $pdo->prepare("DELETE FROM work_experience WHERE cv_id = ?")->execute([$cv_id]);
        $pdo->prepare("DELETE FROM skills WHERE cv_id = ?")->execute([$cv_id]);
        $pdo->prepare("DELETE FROM projects WHERE cv_id = ?")->execute([$cv_id]);
        $pdo->prepare("DELETE FROM certifications WHERE cv_id = ?")->execute([$cv_id]);
        
    } else {
        // Create new CV
        $stmt = $pdo->prepare("INSERT INTO cv_data (user_id, full_name, email, contact, user_image) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$_SESSION['user_id'], $full_name, $email, $contact, $user_image]);
        $cv_id = $pdo->lastInsertId();
    }
    
    // Insert Education
    if (isset($_POST['education']) && is_array($_POST['education'])) {
        $stmt = $pdo->prepare("INSERT INTO education (cv_id, degree, year, institute) VALUES (?, ?, ?, ?)");
        foreach ($_POST['education'] as $edu) {
            if (!empty($edu['degree']) && !empty($edu['year']) && !empty($edu['institute'])) {
                $stmt->execute([
                    $cv_id,
                    sanitize($edu['degree']),
                    sanitize($edu['year']),
                    sanitize($edu['institute'])
                ]);
            }
        }
    }
    
    // Insert Work Experience
    if (isset($_POST['experience']) && is_array($_POST['experience'])) {
        $stmt = $pdo->prepare("INSERT INTO work_experience (cv_id, job_title, company, start_date, end_date, description) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($_POST['experience'] as $exp) {
            if (!empty($exp['job_title']) && !empty($exp['company']) && !empty($exp['start_date'])) {
                $end_date = !empty($exp['end_date']) ? $exp['end_date'] : null;
                $stmt->execute([
                    $cv_id,
                    sanitize($exp['job_title']),
                    sanitize($exp['company']),
                    $exp['start_date'],
                    $end_date,
                    sanitize($exp['description'])
                ]);
            }
        }
    }
    
    // Insert Skills
    if (isset($_POST['skills']) && is_array($_POST['skills'])) {
        $stmt = $pdo->prepare("INSERT INTO skills (cv_id, skill_name, skill_level) VALUES (?, ?, ?)");
        foreach ($_POST['skills'] as $skill) {
            if (!empty($skill['skill_name']) && !empty($skill['skill_level'])) {
                $stmt->execute([
                    $cv_id,
                    sanitize($skill['skill_name']),
                    sanitize($skill['skill_level'])
                ]);
            }
        }
    }
    
    // Insert Projects
    if (isset($_POST['projects']) && is_array($_POST['projects'])) {
        $stmt = $pdo->prepare("INSERT INTO projects (cv_id, project_name, description, github_link) VALUES (?, ?, ?, ?)");
        foreach ($_POST['projects'] as $project) {
            if (!empty($project['project_name']) && !empty($project['description'])) {
                $stmt->execute([
                    $cv_id,
                    sanitize($project['project_name']),
                    sanitize($project['description']),
                    sanitize($project['github_link'])
                ]);
            }
        }
    }
    
    // Insert Certifications
    if (isset($_POST['certifications']) && is_array($_POST['certifications'])) {
        $stmt = $pdo->prepare("INSERT INTO certifications (cv_id, certification_name, issuing_organization, issue_date, expiry_date) VALUES (?, ?, ?, ?, ?)");
        foreach ($_POST['certifications'] as $cert) {
            if (!empty($cert['certification_name'])) {
                $issue_date = !empty($cert['issue_date']) ? $cert['issue_date'] : null;
                $expiry_date = !empty($cert['expiry_date']) ? $cert['expiry_date'] : null;
                $stmt->execute([
                    $cv_id,
                    sanitize($cert['certification_name']),
                    sanitize($cert['issuing_organization']),
                    $issue_date,
                    $expiry_date
                ]);
            }
        }
    }
    
    $pdo->commit();
    
    setSuccessMessage($existing_cv ? 'CV updated successfully!' : 'CV created successfully!');
    header('Location: dashboard.php');
    exit();
    
} catch (Exception $e) {
    $pdo->rollBack();
    setErrorMessage('Error processing CV: ' . $e->getMessage());
    header('Location: cv_form.php');
    exit();
}
?>
