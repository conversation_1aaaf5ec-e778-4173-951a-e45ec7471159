<?php
require_once '../includes/functions.php';
requireLogin();
requireAdmin();

// Get CV ID from URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setErrorMessage('Invalid CV ID.');
    header('Location: cvs.php');
    exit();
}

$cv_id = $_GET['id'];

// Get CV data with user information
$stmt = $pdo->prepare("
    SELECT cv.*, u.full_name as user_name, u.email as user_email, u.created_at as user_registered
    FROM cv_data cv 
    JOIN users u ON cv.user_id = u.id 
    WHERE cv.id = ?
");
$stmt->execute([$cv_id]);
$cv_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$cv_data) {
    setErrorMessage('CV not found.');
    header('Location: cvs.php');
    exit();
}

// Get all CV sections
$education = getUserEducation($cv_id);
$work_experience = getUserWorkExperience($cv_id);
$skills = getUserSkills($cv_id);
$projects = getUserProjects($cv_id);
$certifications = getUserCertifications($cv_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View CV - <?php echo htmlspecialchars($cv_data['full_name']); ?> - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            .cv-preview { box-shadow: none; margin: 0; }
            body { background: white; }
        }
    </style>
</head>
<body>
    <header class="header no-print">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="../index.php">Home</a></li>
                    <li><a href="dashboard.php">Admin Dashboard</a></li>
                    <li><a href="users.php">Manage Users</a></li>
                    <li><a href="cvs.php">View All CVs</a></li>
                    <li><a href="../auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- Admin Info Panel -->
            <div class="card no-print" style="margin-bottom: 2rem;">
                <div class="card-header">
                    <h3 class="card-title">CV Information</h3>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div>
                        <h4>CV Details</h4>
                        <p><strong>CV ID:</strong> <?php echo $cv_data['id']; ?></p>
                        <p><strong>Created:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['created_at'])); ?></p>
                        <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($cv_data['updated_at'])); ?></p>
                    </div>
                    
                    <div>
                        <h4>User Details</h4>
                        <p><strong>User:</strong> <?php echo htmlspecialchars($cv_data['user_name']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($cv_data['user_email']); ?></p>
                        <p><strong>Registered:</strong> <?php echo date('M d, Y', strtotime($cv_data['user_registered'])); ?></p>
                    </div>
                    
                    <div>
                        <h4>CV Sections</h4>
                        <p><strong>Education:</strong> <?php echo count($education); ?> entries</p>
                        <p><strong>Experience:</strong> <?php echo count($work_experience); ?> entries</p>
                        <p><strong>Skills:</strong> <?php echo count($skills); ?> entries</p>
                        <p><strong>Projects:</strong> <?php echo count($projects); ?> entries</p>
                        <p><strong>Certifications:</strong> <?php echo count($certifications); ?> entries</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="cvs.php" class="btn btn-secondary">Back to All CVs</a>
                    <a href="view_user.php?id=<?php echo $cv_data['user_id']; ?>" class="btn btn-primary">View User</a>
                    <button onclick="window.print()" class="btn btn-success">Print CV</button>
                </div>
            </div>

            <!-- CV Preview (Same as user preview but for admin) -->
            <div class="cv-preview">
                <!-- Header Section -->
                <div class="cv-header">
                    <?php if ($cv_data['user_image']): ?>
                        <img src="../uploads/<?php echo htmlspecialchars($cv_data['user_image']); ?>" 
                             alt="Profile Photo" class="cv-photo">
                    <?php endif; ?>
                    <h1><?php echo htmlspecialchars($cv_data['full_name']); ?></h1>
                    <p style="font-size: 1.1rem; color: #666;">
                        <?php echo htmlspecialchars($cv_data['email']); ?> | 
                        <?php echo htmlspecialchars($cv_data['contact']); ?>
                    </p>
                </div>

                <!-- Education Section -->
                <?php if (!empty($education)): ?>
                <div class="cv-section">
                    <h3>Education</h3>
                    <?php foreach ($education as $edu): ?>
                        <div style="margin-bottom: 1rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($edu['degree']); ?>
                            </h4>
                            <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                <?php echo htmlspecialchars($edu['institute']); ?>
                            </p>
                            <p style="color: #666; font-size: 0.9rem;">
                                <?php echo htmlspecialchars($edu['year']); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Work Experience Section -->
                <?php if (!empty($work_experience)): ?>
                <div class="cv-section">
                    <h3>Work Experience</h3>
                    <?php foreach ($work_experience as $exp): ?>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($exp['job_title']); ?>
                            </h4>
                            <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                <?php echo htmlspecialchars($exp['company']); ?>
                            </p>
                            <p style="color: #666; font-size: 0.9rem; margin-bottom: 0.5rem;">
                                <?php echo formatDate($exp['start_date']); ?> - 
                                <?php echo $exp['end_date'] ? formatDate($exp['end_date']) : 'Present'; ?>
                            </p>
                            <?php if ($exp['description']): ?>
                                <p style="color: #555; line-height: 1.6;">
                                    <?php echo nl2br(htmlspecialchars($exp['description'])); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Skills Section -->
                <?php if (!empty($skills)): ?>
                <div class="cv-section">
                    <h3>Skills & Technologies</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <?php foreach ($skills as $skill): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f8f9fa; border-radius: 5px;">
                                <span style="font-weight: 600;"><?php echo htmlspecialchars($skill['skill_name']); ?></span>
                                <span style="color: #667eea; font-size: 0.9rem;"><?php echo htmlspecialchars($skill['skill_level']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Projects Section -->
                <?php if (!empty($projects)): ?>
                <div class="cv-section">
                    <h3>Projects</h3>
                    <?php foreach ($projects as $project): ?>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($project['project_name']); ?>
                                <?php if ($project['github_link']): ?>
                                    <a href="<?php echo htmlspecialchars($project['github_link']); ?>" 
                                       target="_blank" style="font-size: 0.8rem; color: #667eea; text-decoration: none;">
                                        [GitHub]
                                    </a>
                                <?php endif; ?>
                            </h4>
                            <p style="color: #555; line-height: 1.6;">
                                <?php echo nl2br(htmlspecialchars($project['description'])); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Certifications Section -->
                <?php if (!empty($certifications)): ?>
                <div class="cv-section">
                    <h3>Certifications</h3>
                    <?php foreach ($certifications as $cert): ?>
                        <div style="margin-bottom: 1rem;">
                            <h4 style="margin-bottom: 0.25rem; color: #333;">
                                <?php echo htmlspecialchars($cert['certification_name']); ?>
                            </h4>
                            <?php if ($cert['issuing_organization']): ?>
                                <p style="margin-bottom: 0.25rem; font-weight: 600; color: #667eea;">
                                    <?php echo htmlspecialchars($cert['issuing_organization']); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($cert['issue_date'] || $cert['expiry_date']): ?>
                                <p style="color: #666; font-size: 0.9rem;">
                                    <?php if ($cert['issue_date']): ?>
                                        Issued: <?php echo formatDate($cert['issue_date']); ?>
                                    <?php endif; ?>
                                    <?php if ($cert['expiry_date']): ?>
                                        | Expires: <?php echo formatDate($cert['expiry_date']); ?>
                                    <?php endif; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="footer no-print">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
