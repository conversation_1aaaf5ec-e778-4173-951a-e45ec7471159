<?php
// Demo Admin Panel - Shows what the admin panel looks like
session_start();

// Simulate admin login for demo
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin User';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_type'] = 'admin';

// Mock data for demonstration
$total_users = 15;
$total_cvs = 12;
$total_education = 28;
$total_experience = 35;

$recent_users = [
    ['id' => 1, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-15 10:30:00'],
    ['id' => 2, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-14 14:20:00'],
    ['id' => 3, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-13 09:15:00'],
    ['id' => 4, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-12 16:45:00'],
    ['id' => 5, 'full_name' => 'David <PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-11 11:30:00']
];

$recent_cvs = [
    ['id' => 1, 'full_name' => '<PERSON>', 'email' => '<EMAIL>', 'created_at' => '2024-01-15 11:00:00', 'user_name' => 'John Doe'],
    ['id' => 2, 'full_name' => 'Jane Smith', 'email' => '<EMAIL>', 'created_at' => '2024-01-14 15:30:00', 'user_name' => 'Jane Smith'],
    ['id' => 3, 'full_name' => 'Mike Johnson', 'email' => '<EMAIL>', 'created_at' => '2024-01-13 10:45:00', 'user_name' => 'Mike Johnson'],
    ['id' => 4, 'full_name' => 'Sarah Wilson', 'email' => '<EMAIL>', 'created_at' => '2024-01-12 17:20:00', 'user_name' => 'Sarah Wilson']
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - CV Builder</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">CV Builder - Admin</div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="admin_demo.php">Admin Dashboard</a></li>
                    <li><a href="admin_users_demo.php">Manage Users</a></li>
                    <li><a href="admin_cvs_demo.php">View All CVs</a></li>
                    <li><a href="auth/logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Admin Dashboard</h1>
                    <p>Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?>! Manage your CV Builder platform.</p>
                </div>

                <!-- Statistics -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_users; ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_cvs; ?></div>
                        <div class="stat-label">CVs Created</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_education; ?></div>
                        <div class="stat-label">Education Entries</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_experience; ?></div>
                        <div class="stat-label">Work Experiences</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <a href="admin_users_demo.php" class="btn btn-primary">Manage Users</a>
                        <a href="admin_cvs_demo.php" class="btn btn-secondary">View All CVs</a>
                        <a href="dashboard.php" class="btn btn-success">User Dashboard</a>
                    </div>
                </div>

                <!-- Recent Users -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Users</h3>
                    </div>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Registered</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <a href="#" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="admin_users_demo.php" class="btn btn-primary">View All Users</a>
                    </div>
                </div>

                <!-- Recent CVs -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent CVs</h3>
                    </div>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>CV ID</th>
                                <th>CV Name</th>
                                <th>User</th>
                                <th>Email</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_cvs as $cv): ?>
                                <tr>
                                    <td><?php echo $cv['id']; ?></td>
                                    <td><?php echo htmlspecialchars($cv['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($cv['user_name']); ?></td>
                                    <td><?php echo htmlspecialchars($cv['email']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($cv['created_at'])); ?></td>
                                    <td>
                                        <a href="#" class="btn btn-secondary" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="admin_cvs_demo.php" class="btn btn-primary">View All CVs</a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CV Builder. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
