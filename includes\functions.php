<?php
session_start();
require_once __DIR__ . '/../config/database.php';

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: auth/login.php');
        exit();
    }
}

// Redirect if not admin
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: dashboard.php');
        exit();
    }
}

// Sanitize input
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Upload image
function uploadImage($file, $target_dir = 'uploads/') {
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }

    $target_file = $target_dir . basename($file["name"]);
    $uploadOk = 1;
    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));

    // Check if image file is actual image
    if(isset($_POST["submit"])) {
        $check = getimagesize($file["tmp_name"]);
        if($check !== false) {
            $uploadOk = 1;
        } else {
            $uploadOk = 0;
        }
    }

    // Check file size (5MB max)
    if ($file["size"] > 5000000) {
        $uploadOk = 0;
    }

    // Allow certain file formats
    if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif" ) {
        $uploadOk = 0;
    }

    // Generate unique filename
    $unique_name = uniqid() . '.' . $imageFileType;
    $target_file = $target_dir . $unique_name;

    if ($uploadOk == 1) {
        if (move_uploaded_file($file["tmp_name"], $target_file)) {
            return $unique_name;
        }
    }
    return false;
}

// Get user CV data
function getUserCVData($user_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM cv_data WHERE user_id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get user education
function getUserEducation($cv_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM education WHERE cv_id = ?");
    $stmt->execute([$cv_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get user work experience
function getUserWorkExperience($cv_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM work_experience WHERE cv_id = ?");
    $stmt->execute([$cv_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get user skills
function getUserSkills($cv_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM skills WHERE cv_id = ?");
    $stmt->execute([$cv_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get user projects
function getUserProjects($cv_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM projects WHERE cv_id = ?");
    $stmt->execute([$cv_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get user certifications
function getUserCertifications($cv_id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM certifications WHERE cv_id = ?");
    $stmt->execute([$cv_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Format date
function formatDate($date) {
    return date('F Y', strtotime($date));
}

// Success message
function setSuccessMessage($message) {
    $_SESSION['success_message'] = $message;
}

// Error message
function setErrorMessage($message) {
    $_SESSION['error_message'] = $message;
}

// Display messages
function displayMessages() {
    if (isset($_SESSION['success_message'])) {
        echo '<div class="alert alert-success">' . $_SESSION['success_message'] . '</div>';
        unset($_SESSION['success_message']);
    }

    if (isset($_SESSION['error_message'])) {
        echo '<div class="alert alert-danger">' . $_SESSION['error_message'] . '</div>';
        unset($_SESSION['error_message']);
    }
}
?>
